import { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  Text,
  Heading,
  Badge,
  Progress,
  useToast,
  Spinner,
  Center,
  HStack,
  Icon,
  useColorModeValue,
  Divider,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Card,
  CardBody,
  CardHeader,
  List,
  ListItem,
  ListIcon,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Alert,
  AlertIcon,
  AlertDescription,
  Avatar,
} from '@chakra-ui/react';
import { ViewIcon, StarIcon, TimeIcon, CheckCircleIcon, WarningIcon, ArrowUpIcon } from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import DataService, { type PracticeResult, type AdvancedFeedback } from '../lib/unified-data';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { fadeIn, staggerContainer, staggerItem } from '../motion/index';
import { mockCandidates } from './IntegratedLinkWorkflow/constants/mockData';
import { CandidateInfo } from './IntegratedLinkWorkflow/types';
import { 
  CandidateCard, 
  SectionHeader, 
  UnifiedContainer,
  UnifiedTabList 
} from './shared/UnifiedCard';

const MotionBox = motion.create(Box);

export const FeedbackViewer = () => {
  const [practiceResults, setPracticeResults] = useState<PracticeResult[]>([]);
  const [selectedResult, setSelectedResult] = useState<PracticeResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const toast = useToast();
  
  // カラーモード対応
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const emptyBg = useColorModeValue('gray.50', 'gray.700');
  const cardHoverBg = useColorModeValue('gray.50', 'gray.700');

  /**
   * 統一データシステムから詳細分析データを取得
   */
  useEffect(() => {
    const fetchResults = async () => {
      try {
        setIsLoading(true);
        console.log('📊 詳細分析データを取得中...');
        
        // 統一データシステムから面接結果を取得
        const results = DataService.getAllPracticeResults();
        
        setPracticeResults(results);
        console.log('✅ 詳細分析データ取得完了:', results.length, '件');
        
        if (results.length > 0) {
          setSelectedResult(results[0]); // 最初の結果を選択
        }
        
      } catch (error) {
        console.error('詳細分析データの取得に失敗:', error);
        toast({
          title: '詳細分析データの取得に失敗しました',
          description: '統一データシステムからのデータ取得に失敗しました',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [toast]);

  if (isLoading) {
    return (
      <MotionBox {...fadeIn}>
        <Center p={spacing.cardPadding}>
          <VStack spacing={4}>
            <Spinner size="xl" color="blue.500" thickness="4px" />
            <Text {...textStyles.body} color="gray.500">
              詳細分析データを読み込み中...
            </Text>
          </VStack>
        </Center>
      </MotionBox>
    );
  }

  if (practiceResults.length === 0) {
    return (
      <MotionBox {...fadeIn}>
        <Box 
          p={spacing.cardPadding} 
          bg={emptyBg} 
          borderRadius="md" 
          textAlign="center"
          borderWidth="1px"
          borderColor={borderColor}
        >
          <VStack spacing={4}>
            <Icon as={ViewIcon} boxSize={12} color="gray.400" />
            <Text {...textStyles.body} color="gray.500">
              分析結果はまだありません
            </Text>
            <Text {...textStyles.caption} color="gray.400">
              面接が実施されると、ここに詳細な分析結果が表示されます
            </Text>
          </VStack>
        </Box>
      </MotionBox>
    );
  }

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'green';
    if (score >= 6) return 'yellow';
    return 'red';
  };

  const getLevelBadgeColor = (level: string) => {
    switch (level) {
      case 'expert': return 'purple';
      case 'senior': return 'blue';
      case 'mid': return 'green';
      case 'junior': return 'orange';
      default: return 'gray';
    }
  };

  // ヘルパー関数
  const getAverageScore = (result: PracticeResult, scoreType: keyof AdvancedFeedback) => {
    const scores = result.feedback
      .map(f => f[scoreType])
      .filter(s => typeof s === 'number') as number[];
    return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
  };

  const getCandidateInfo = (candidateName: string): CandidateInfo | null => {
    return mockCandidates.find(candidate => candidate.name === candidateName) || null;
  };

  return (
    <UnifiedContainer width="100%">
      {/* 候補者一覧（統一されたデザイン） */}
      <Box width="100%">
        <SectionHeader 
          title="📊 面接結果一覧" 
          count={practiceResults.length}
          width="100%"
        />

        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4} mb={6} width="100%">
          {practiceResults.map((result) => {
            const candidateInfo = result.candidateName ? getCandidateInfo(result.candidateName) : null;
            return (
              <CandidateCard
                key={result.id}
                name={result.candidateName || '名前なし'}
                nameKana={candidateInfo?.nameKana || 'かな情報なし'}
                age={candidateInfo?.age || 0}
                currentStatus={candidateInfo?.currentStatus || 'unemployed'}
                currentEmployer={candidateInfo?.currentEmployer}
                previousEmployer={candidateInfo?.previousEmployer}
                position={candidateInfo?.position || '職種不明'}
                address={candidateInfo?.address || '住所不明'}
                resumeFile={candidateInfo?.resumeFile?.name || ''}
                careerHistoryFile={candidateInfo?.careerHistoryFile?.name || ''}
                score={result.overallScore}
                isSelected={selectedResult?.id === result.id}
                onClick={() => setSelectedResult(result)}
                additionalInfo={
                  <VStack align="start" spacing={1} w="100%">
                    <HStack spacing={2} w="100%">
                      <Text fontSize="xs" color="gray.400" minW="40px">面接先</Text>
                      <Text fontSize="sm" color="purple.600" noOfLines={1} fontWeight="medium">
                        {result.companyName}
                      </Text>
                    </HStack>
                    <HStack spacing={2} w="100%">
                      <Text fontSize="xs" color="gray.400" minW="40px">職種</Text>
                      <Text fontSize="sm" color="purple.600" noOfLines={1}>
                        {result.position}
                      </Text>
                    </HStack>
                    <HStack justify="space-between" w="100%">
                      <HStack spacing={1}>
                        <Text fontSize="xs" color="gray.400">実施日</Text>
                        <Text fontSize="xs" color="gray.600">
                          {new Date(result.practiceDate).toLocaleDateString()}
                        </Text>
                      </HStack>
                      <Badge colorScheme="gray" size="xs" variant="outline">
                        {result.duration}分
                      </Badge>
                    </HStack>
                  </VStack>
                }
              />
            );
          })}
        </SimpleGrid>
      </Box>

        {/* 詳細分析表示 */}
        {selectedResult && (
          <MotionBox {...fadeIn}>
            <Card bg={bgColor} borderColor={borderColor}>
              <CardHeader>
                <HStack justify="space-between" align="center">
                  <VStack align="start" spacing={1}>
                    <Heading size="md" color="blue.600">
                      {selectedResult.candidateName}の詳細分析
                    </Heading>
                    <HStack spacing={3}>
                      <Text fontSize="sm" color="gray.600">
                        {selectedResult.companyName} | {selectedResult.position}
                      </Text>
                      <Badge colorScheme="blue">
                        {DataService.getScenarioDisplayName(selectedResult.scenarioId)}
                      </Badge>
                    </HStack>
                  </VStack>
                  <VStack align="end" spacing={1}>
                    <Badge colorScheme={getScoreColor(selectedResult.overallScore)} fontSize="lg" p={2}>
                      総合スコア: {selectedResult.overallScore.toFixed(1)}/10
                    </Badge>
                    <Text fontSize="sm" color="gray.500">
                      面接時間: {selectedResult.duration}分
                    </Text>
                  </VStack>
                </HStack>
              </CardHeader>

              <CardBody>
                <Tabs variant="soft-rounded" colorScheme="blue">
                  <UnifiedTabList width="100%">
                    <TabList 
                      mb={4} 
                      overflowX="auto" 
                      flexWrap="nowrap"
                      width="100%"
                    >
                      <Tab 
                        fontSize={{ base: "xs", sm: "sm", md: "md" }}
                        py={{ base: 2, md: 3 }}
                        px={{ base: 3, md: 4 }}
                        minW="fit-content"
                        whiteSpace="nowrap"
                        flex="none"
                      >
                        📊 スキル分析
                      </Tab>
                      <Tab 
                        fontSize={{ base: "xs", sm: "sm", md: "md" }}
                        py={{ base: 2, md: 3 }}
                        px={{ base: 3, md: 4 }}
                        minW="fit-content"
                        whiteSpace="nowrap"
                        flex="none"
                      >
                        💬 質問別詳細
                      </Tab>
                      <Tab 
                        fontSize={{ base: "xs", sm: "sm", md: "md" }}
                        py={{ base: 2, md: 3 }}
                        px={{ base: 3, md: 4 }}
                        minW="fit-content"
                        whiteSpace="nowrap"
                        flex="none"
                      >
                        📈 改善提案
                      </Tab>
                    </TabList>
                  </UnifiedTabList>

                  <TabPanels>
                    {/* スキル分析タブ */}
                    <TabPanel p={0}>
                      <VStack spacing={6} align="stretch">
                        {/* 総合統計 */}
                        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
                          <Stat bg={emptyBg} p={3} borderRadius="md">
                            <StatLabel fontSize="xs">コミュニケーション</StatLabel>
                            <StatNumber color={getScoreColor(getAverageScore(selectedResult, 'communicationSkill')) + '.600'}>
                              {getAverageScore(selectedResult, 'communicationSkill').toFixed(1)}
                            </StatNumber>
                            <StatHelpText>/10</StatHelpText>
                          </Stat>
                          <Stat bg={emptyBg} p={3} borderRadius="md">
                            <StatLabel fontSize="xs">問題解決力</StatLabel>
                            <StatNumber color={getScoreColor(getAverageScore(selectedResult, 'problemSolvingAbility')) + '.600'}>
                              {getAverageScore(selectedResult, 'problemSolvingAbility').toFixed(1)}
                            </StatNumber>
                            <StatHelpText>/10</StatHelpText>
                          </Stat>
                          <Stat bg={emptyBg} p={3} borderRadius="md">
                            <StatLabel fontSize="xs">企業適合性</StatLabel>
                            <StatNumber color={getScoreColor(getAverageScore(selectedResult, 'culturalFit')) + '.600'}>
                              {getAverageScore(selectedResult, 'culturalFit').toFixed(1)}
                            </StatNumber>
                            <StatHelpText>/10</StatHelpText>
                          </Stat>
                          <Stat bg={emptyBg} p={3} borderRadius="md">
                            <StatLabel fontSize="xs">ストレス耐性</StatLabel>
                            <StatNumber color={getScoreColor(getAverageScore(selectedResult, 'stressHandling')) + '.600'}>
                              {getAverageScore(selectedResult, 'stressHandling').toFixed(1)}
                            </StatNumber>
                            <StatHelpText>/10</StatHelpText>
                          </Stat>
                        </SimpleGrid>

                        {/* 業界ベンチマーク */}
                        <Alert status="info" borderRadius="md">
                          <AlertIcon />
                          <AlertDescription>
                            業界ベンチマーク: {selectedResult.feedback[0]?.industryBenchmark || 'N/A'}% 
                            | レベル評価: 
                            <Badge ml={2} colorScheme={getLevelBadgeColor(selectedResult.feedback[0]?.levelComparison || 'mid')}>
                              {selectedResult.feedback[0]?.levelComparison?.toUpperCase() || 'MID'}
                            </Badge>
                          </AlertDescription>
                        </Alert>
                      </VStack>
                    </TabPanel>

                    {/* 質問別詳細タブ */}
                    <TabPanel p={0}>
                      <VStack spacing={6} align="stretch">
                        {selectedResult.feedback.map((feedback, index) => {
                          const question = DataService.getQuestionById(feedback.questionId);
                          return (
                            <Card key={feedback.id} variant="outline">
                              <CardHeader pb={2}>
                                <HStack justify="space-between">
                                  <Text fontWeight="bold" fontSize="md">
                                    質問 {index + 1}: {question?.category || '一般'}
                                  </Text>
                                  <Badge colorScheme={getScoreColor(feedback.communicationSkill)}>
                                    {feedback.communicationSkill}/10
                                  </Badge>
                                </HStack>
                              </CardHeader>
                              <CardBody pt={0}>
                                <VStack align="stretch" spacing={4}>
                                  <Box>
                                    <Text fontSize="sm" fontWeight="bold" mb={2} color="gray.600">
                                      🤔 質問内容
                                    </Text>
                                    <Text fontSize="sm" bg={emptyBg} p={3} borderRadius="md">
                                      {question?.text || '質問が見つかりません'}
                                    </Text>
                                  </Box>
                                  
                                  <Box>
                                    <Text fontSize="sm" fontWeight="bold" mb={2} color="gray.600">
                                      💬 候補者の回答
                                    </Text>
                                    <Text fontSize="sm" lineHeight="tall">
                                      {feedback.candidateAnswer}
                                    </Text>
                                  </Box>

                                  <Box>
                                    <Text fontSize="sm" fontWeight="bold" mb={2} color="gray.600">
                                      🤖 AI分析
                                    </Text>
                                    <Text fontSize="sm" bg={emptyBg} p={3} borderRadius="md" lineHeight="tall">
                                      {feedback.aiAnalysis}
                                    </Text>
                                  </Box>
                                </VStack>
                              </CardBody>
                            </Card>
                          );
                        })}
                      </VStack>
                    </TabPanel>

                    {/* 改善提案タブ */}
                    <TabPanel p={0}>
                      <VStack spacing={6} align="stretch">
                        {selectedResult.feedback.map((feedback, index) => (
                          <Box key={feedback.id}>
                            <Text fontWeight="bold" mb={4} color="blue.600">
                              質問 {index + 1} の改善提案
                            </Text>
                            
                            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                              {/* 強み */}
                              <Card variant="outline" borderColor="green.200">
                                <CardHeader pb={2}>
                                  <HStack>
                                    <Icon as={CheckCircleIcon} color="green.500" />
                                    <Text fontWeight="bold" fontSize="sm" color="green.600">
                                      強み
                                    </Text>
                                  </HStack>
                                </CardHeader>
                                <CardBody pt={0}>
                                  <List spacing={2}>
                                    {feedback.strengths.map((strength, i) => (
                                      <ListItem key={i} fontSize="sm">
                                        <ListIcon as={CheckCircleIcon} color="green.500" />
                                        {strength}
                                      </ListItem>
                                    ))}
                                  </List>
                                </CardBody>
                              </Card>

                              {/* 改善点 */}
                              <Card variant="outline" borderColor="orange.200">
                                <CardHeader pb={2}>
                                  <HStack>
                                    <Icon as={WarningIcon} color="orange.500" />
                                    <Text fontWeight="bold" fontSize="sm" color="orange.600">
                                      改善点
                                    </Text>
                                  </HStack>
                                </CardHeader>
                                <CardBody pt={0}>
                                  <List spacing={2}>
                                    {feedback.improvements.map((improvement, i) => (
                                      <ListItem key={i} fontSize="sm">
                                        <ListIcon as={WarningIcon} color="orange.500" />
                                        {improvement}
                                      </ListItem>
                                    ))}
                                  </List>
                                </CardBody>
                              </Card>

                              {/* 次のステップ */}
                              <Card variant="outline" borderColor="blue.200">
                                <CardHeader pb={2}>
                                  <HStack>
                                    <Icon as={ArrowUpIcon} color="blue.500" />
                                    <Text fontWeight="bold" fontSize="sm" color="blue.600">
                                      次のステップ
                                    </Text>
                                  </HStack>
                                </CardHeader>
                                <CardBody pt={0}>
                                  <List spacing={2}>
                                    {feedback.nextSteps.map((step, i) => (
                                      <ListItem key={i} fontSize="sm">
                                        <ListIcon as={ArrowUpIcon} color="blue.500" />
                                        {step}
                                      </ListItem>
                                    ))}
                                  </List>
                                </CardBody>
                              </Card>
                            </SimpleGrid>
                            
                            {index < selectedResult.feedback.length - 1 && <Divider mt={6} />}
                          </Box>
                        ))}
                      </VStack>
                    </TabPanel>
                  </TabPanels>
                </Tabs>
              </CardBody>
            </Card>
          </MotionBox>
        )}
    </UnifiedContainer>
  );
}; 