/**
 * ステップ0: 候補者選択・追加
 */

import React from 'react';
import {
  Alert,
  AlertIcon,
  AlertDescription,
  VStack,
  HStack,
  Button,
  Text,
  Box,
  Badge,
  SimpleGrid,
  Icon,
  useToast
} from '@chakra-ui/react';
import { FiUsers } from 'react-icons/fi';
import { EditIcon } from '@chakra-ui/icons';
import { CandidateInfo } from '../types';
import { 
  CandidateCard, 
  SectionHeader, 
  UnifiedContainer 
} from '../../shared/UnifiedCard';

interface CandidateSelectionStepProps {
  selectedCandidate: CandidateInfo | null;
  existingCandidates: CandidateInfo[];
  showNewCandidateForm: boolean;
  onCandidateSelect: (candidate: CandidateInfo) => void;
  onShowNewCandidateForm: () => void;
  onEditCandidate: () => void;
  onNextStep: () => void;
  loadExistingCandidates: () => void;
}

export const CandidateSelectionStep: React.FC<CandidateSelectionStepProps> = ({
  selectedCandidate,
  existingCandidates,
  showNewCandidateForm,
  onCandidateSelect,
  onShowNewCandidateForm,
  onEditCandidate,
  onNextStep,
  loadExistingCandidates
}) => {
  const toast = useToast();

  // 候補者選択時の処理
  const handleCandidateSelect = (candidate: CandidateInfo) => {
    onCandidateSelect(candidate);
    
    toast({
      title: '候補者選択完了',
      description: `${candidate.name}さんを選択しました`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  React.useEffect(() => {
    loadExistingCandidates();
  }, []);

  return (
    <UnifiedContainer width="100%">
      <Alert status="info">
        <AlertIcon />
        <AlertDescription fontSize="sm">
          既存の候補者から選択、または新規候補者を追加してください。
        </AlertDescription>
      </Alert>

      {/* 候補者選択ボタン - レスポンシブ対応 */}
      <VStack spacing={3} align="stretch" display={{ base: "flex", md: "none" }}>
        <Button
          size="md"
          variant={!showNewCandidateForm ? "solid" : "outline"}
          colorScheme="blue"
          onClick={() => {}}
          w="100%"
        >
          📋 既存候補者から選択
        </Button>
        
        <Button
          size="md"
          variant={showNewCandidateForm ? "solid" : "outline"}
          colorScheme="green"
          onClick={onShowNewCandidateForm}
          w="100%"
        >
          ➕ 新規候補者を追加
        </Button>
      </VStack>

      <HStack spacing={4} display={{ base: "none", md: "flex" }}>
        <Button
          size="md"
          variant={!showNewCandidateForm ? "solid" : "outline"}
          colorScheme="blue"
          onClick={() => {}}
        >
          📋 既存候補者から選択
        </Button>
        
        <Button
          size="md"
          variant={showNewCandidateForm ? "solid" : "outline"}
          colorScheme="green"
          onClick={onShowNewCandidateForm}
        >
          ➕ 新規候補者を追加
        </Button>
      </HStack>

      {/* 既存候補者一覧 */}
      {!showNewCandidateForm && (
        <Box width="100%">
          <SectionHeader 
            title="登録済み候補者" 
            count={existingCandidates.length}
            width="100%"
          />

          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4} width="100%">
            {existingCandidates.map((candidate) => (
              <CandidateCard
                key={candidate.id}
                name={candidate.name}
                nameKana={candidate.nameKana}
                age={candidate.age}
                currentStatus={candidate.currentStatus}
                currentEmployer={candidate.currentEmployer}
                previousEmployer={candidate.previousEmployer}
                position={candidate.position}
                address={candidate.address}
                resumeFile={candidate.resumeFile ? candidate.resumeFile.name : ''}
                careerHistoryFile={candidate.careerHistoryFile ? candidate.careerHistoryFile.name : ''}
                isSelected={selectedCandidate?.id === candidate.id}
                onClick={() => handleCandidateSelect(candidate)}
                topRightBadge={selectedCandidate?.id === candidate.id ? (
                  <Button
                    size="xs"
                    variant="outline"
                    colorScheme="blue"
                    leftIcon={<EditIcon />}
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditCandidate();
                    }}
                  >
                    編集
                  </Button>
                ) : undefined}
                additionalInfo={candidate.activeLinks.length > 0 ? (
                  <Box w="100%" pt={2} borderTop="1px" borderColor="gray.100">
                    <HStack spacing={2} mb={1}>
                      <Text fontSize="xs" color="blue.600" fontWeight="medium">
                        🔗 発行済みリンク
                      </Text>
                      <Badge colorScheme="blue" size="xs" variant="subtle">
                        {candidate.activeLinks.length}件
                      </Badge>
                    </HStack>
                    <VStack align="start" spacing={0.5}>
                      {candidate.activeLinks.slice(0, 2).map((link: any) => (
                        <Text key={link.id} fontSize="xs" color="gray.500" noOfLines={1}>
                          • {link.companyName} - {link.position}
                        </Text>
                      ))}
                      {candidate.activeLinks.length > 2 && (
                        <Text fontSize="xs" color="gray.400">
                          他{candidate.activeLinks.length - 2}件...
                        </Text>
                      )}
                    </VStack>
                  </Box>
                ) : undefined}
              />
            ))}
          </SimpleGrid>
        </Box>
      )}

      {/* アクションボタン - レスポンシブ対応 */}
      <VStack spacing={3} align="stretch" width="100%">
        <Button
          colorScheme="blue"
          onClick={onNextStep}
          isDisabled={!selectedCandidate}
          size={{ base: "md", md: "lg" }}
          w={{ base: "100%", md: "auto" }}
          alignSelf={{ base: "stretch", md: "flex-end" }}
        >
          <Text display={{ base: "block", md: "none" }}>次へ</Text>
          <Text display={{ base: "none", md: "block" }}>次へ：企業選択</Text>
        </Button>
      </VStack>
    </UnifiedContainer>
  );
};