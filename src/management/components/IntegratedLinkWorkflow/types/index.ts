/**
 * IntegratedLinkWorkflow関連の型定義
 */

export interface CompanyDocument {
  id: string;
  fileName: string;
  fileSize: number;
  uploadedAt: string;
  fileType: 'pdf' | 'doc' | 'text';
  content?: string; // テキストコンテンツ
  originFile?: File; // Lưu file gốc để upload lên API
}

export interface AIGeneratedIntent {
  id: string;
  category: string;
  intent: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number;
  aiInstructions: string;
  confidence: number; // AI の生成信頼度
  isEdited: boolean;
}

export interface IntegratedLinkWorkflowProps {
  onLinkGenerated?: (linkData: any) => void;
  existingLinkId?: string; // 既存リンクの編集時
}

export interface IntentEditFormProps {
  intent: AIGeneratedIntent;
  onSave: (updatedIntent: AIGeneratedIntent) => void;
  onCancel: () => void;
}

// 候補者関連の型
export interface CandidateInfo {
  id: string;
  name: string;
  nameKana: string;
  email?: string;
  age: number;
  candidateCompany: string;
  candidatePosition: string;
  currentStatus: 'employed' | 'unemployed';
  currentEmployer?: string;
  previousEmployer?: string;
  position: string;
  address: string;
  mainSkill?: string;
  otherSkill?: string;
  experience?: string;
  resumeFile?: File | null;
  careerHistoryFile?: File | null;
  activeLinks: any[];
  profile?: any;
}

// 企業関連の型
export interface CompanyInfo {
  id: string;
  name: string;
  industry: string;
  size: string;
  description: string;
  website?: string;
  address: string;
  foundedYear: number;
  businessType: 'BtoB' | 'BtoC' | 'BtoBtoC';
  tags: string[];
}

// ワークフローの状態管理用の型
export interface WorkflowState {
  currentStep: number;
  isLoading: boolean;
  uploadedDocs: CompanyDocument[];
  generatedIntents: AIGeneratedIntent[];
  generatedQuestions: any[];
  candidateName: string;
  candidateEmail: string;
  additionalNotes: string;
  companyNameInput: string;
  positionInput: string;
  selectedCompanyId: string | null;
  selectedTemplate: any;
  candidateProfile: any;
  enableProfileIntegration: boolean;
  showTemplateSelector: boolean;
  templateName: string;
  shouldSaveAsTemplate: boolean;
  selectedCandidate: CandidateInfo | null;
  showNewCandidateForm: boolean;
  candidateNameKana: string;
  candidateAge: string;
  candidateStatus: string;
  candidateCompany: string;
  candidatePosition: string;
  candidateAddress: string;
  // candidateHasResume: boolean;
  // candidateHasCareerHistory: boolean;
  candidateResumeFile: string;
  candidateCareerHistoryFile: string;
}