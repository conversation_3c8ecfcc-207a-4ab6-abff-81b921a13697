export interface CandidateApiResponse {
  id: string;
  userId: string;
  name?: string;
  nameKana: string;
  email: string;
  age: number;
  employment_status: 'employed' | 'unemployed';
  company?: string;
  position?: string;
  address?: string;
  resumeFile?: string;
  careerHistoryFile?: string;
  profile?: any;
  mainSkill?: string;
  otherSkill?: string;
  experience?: string;
  education?: string;
  language?: string;
  otherInfo?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CandidateInfo {
  id: string;
  name?: string;
  nameKana: string;
  email?: string;
  age: number;
  candidateStatus: string;
  employmentStatus: string;
  currentEmployer?: string;
  previousEmployer?: string;
  position?: string;
  address?: string;
  resumeFile?: string;
  careerHistoryFile?: string;
  otherSkill?: string;
  activeLinks: any[];
  profile?: any;
  company?: string;
  experience?: string;
}
