from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from app.models.keypoint_template import KeypointTemplate
from app.models.company import Company
from app.models.base import Base
from datetime import datetime
import uuid
from app.core.config import settings


def setup_database_connection():
    engine = create_engine(settings.DATABASE_URL)
    Base.metadata.bind = engine
    return Session(bind=engine)


def get_default_keypoint_templates():
    return [
        {
            "id": uuid.UUID("550e8400-e29b-41d4-a716-************"),
            "name": "企業理解の深さと志望動機",
            "description": "アップロードされた企業資料を基に、候補者がどの程度企業を理解しているか、表面的でない本当の志望理由を確認する。候補者の企業研究の深さと真の志望動機を探る。",
            "position_type": "general",
            "industry": "general",
            "difficulty_level": 3,
            "is_default": True,
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "id": uuid.UUID("550e8400-e29b-41d4-a716-************"),
            "name": "技術適合性の確認",
            "description": "求人票の要件と候補者のスキル・経験の合致度を具体的なエピソードで確認。求人要件と候補者スキルの具体的な合致度を確認。",
            "position_type": "developer",
            "industry": "IT",
            "difficulty_level": 4,
            "is_default": True,
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "id": uuid.UUID("550e8400-e29b-41d4-a716-************"),
            "name": "キャリア展望の評価",
            "description": "候補者の将来的な目標と、それが企業の方向性と合致するかを評価。将来ビジョンと企業成長戦略の整合性を評価。",
            "position_type": "general",
            "industry": "general",
            "difficulty_level": 2,
            "is_default": True,
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    ]

def get_or_create_default_company(session: Session):
    default_company = session.query(Company).filter_by(name="デフォルト企業").first()
    
    if not default_company:
        default_company = Company(
            id=uuid.UUID("550e8400-e29b-41d4-a716-************"),
            name="デフォルト企業",
            industry="IT",
            description="デフォルトの企業プロファイル",
            core_values=["革新性", "品質", "チームワーク"],
            vision_mission="デジタル時代のリーディングカンパニーを目指す",
            created_at=datetime.utcnow()
        )
        session.add(default_company)
        print("Created new default company")
    else:
        if default_company.core_values and isinstance(default_company.core_values, dict):
            print(f"Updating core_values format for existing company: {default_company.name}")
            default_company.core_values = ["革新性", "品質", "チームワーク"]
        else:
            print(f"Using existing company: {default_company.name}")
    
    session.commit()
    session.refresh(default_company)
    return default_company

def create_or_update_keypoint_template(session: Session, template_data: dict, company_id: uuid.UUID):

    existing_template = session.query(KeypointTemplate).filter_by(id=template_data["id"]).first()
    
    if existing_template:
        for key, value in template_data.items():
            if key != "id":
                setattr(existing_template, key, value)
        print(f"Updated existing template: {template_data['name']}")
    else:
        template = KeypointTemplate(
            id=template_data["id"],
            company_id=company_id,
            name=template_data["name"],
            description=template_data["description"],
            position_type=template_data["position_type"],
            industry=template_data["industry"],
            difficulty_level=template_data["difficulty_level"],
            is_default=template_data["is_default"],
            is_active=template_data["is_active"],
            created_at=template_data["created_at"],
            updated_at=template_data["updated_at"]
        )
        session.add(template)
        print(f"Created new template: {template_data['name']}")

def seed_keypoint_templates(session: Session, company_id: uuid.UUID):
    templates = get_default_keypoint_templates()
    
    for template_data in templates:
        create_or_update_keypoint_template(session, template_data, company_id)
    
    session.commit()

def main():
    print("Starting Keypoint Template Seeding Process...")
    
    session = setup_database_connection()
    
    try:
        print("\nStep 1: Setting up default company...")
        default_company = get_or_create_default_company(session)
        
        print("\nStep 2: Creating keypoint templates...")
        seed_keypoint_templates(session, default_company.id)
        
        print("\nSeeding process completed successfully!")
        
    except Exception as e:
        print(f"Error during seeding: {str(e)}")
        session.rollback()
        raise
    finally:
        session.close()

if __name__ == "__main__":
    main()
