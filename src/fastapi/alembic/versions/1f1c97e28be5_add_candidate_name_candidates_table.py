"""add_candidate_name_candidates_table

Revision ID: 1f1c97e28be5
Revises: a9c356c6f758
Create Date: 2025-07-09 09:59:58.719160

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1f1c97e28be5'
down_revision: Union[str, None] = 'a9c356c6f758'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.add_column('candidates', sa.Column('name', sa.String(), nullable=True))



def downgrade() -> None:
    """Downgrade schema."""
    op.drop_column('candidates', 'name')

