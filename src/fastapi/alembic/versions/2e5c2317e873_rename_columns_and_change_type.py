"""rename columns and change type

Revision ID: 2e5c2317e873
Revises: 1f1c97e28be5
Create Date: 2025-07-10 10:16:23.565032

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2e5c2317e873'
down_revision: Union[str, None] = '1f1c97e28be5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

    #  candidateResumeFile={candidateState.candidateResumeFile}
    #   candidateCareerHistoryFile={candidateState.candidateCareerHistoryFile}

def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('candidates', 'has_resume', new_column_name='resume_file')
    op.alter_column('candidates', 'has_career_history', new_column_name='career_history_file')

    # 2. <PERSON><PERSON><PERSON> ki<PERSON>u dữ liệu từ Boolean → String
    op.alter_column('candidates', 'resume_file',
        existing_type=sa.Boolean(),
        type_=sa.String(),
        existing_nullable=True
    )

    op.alter_column('candidates', 'career_history_file',
        existing_type=sa.Boolean(),
        type_=sa.String(),
        existing_nullable=True
    )


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('candidates', 'resume_file', new_column_name='has_resume')
    op.alter_column('candidates', 'career_history_file', new_column_name='has_career_history')

    op.alter_column('candidates', 'has_resume',
        existing_type=sa.String(),
        type_=sa.Boolean(),
        existing_nullable=True
    )

    op.alter_column('candidates', 'has_career_history',
        existing_type=sa.String(),
        type_=sa.Boolean(),
        existing_nullable=True
    )

