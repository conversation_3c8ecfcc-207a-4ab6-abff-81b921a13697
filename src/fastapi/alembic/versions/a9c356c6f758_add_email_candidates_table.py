"""add_email_candidates_table

Revision ID: a9c356c6f758
Revises: ede76d80a347
Create Date: 2025-07-09 08:42:08.847867

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a9c356c6f758'
down_revision: Union[str, None] = 'ede76d80a347'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.add_column('candidates', sa.Column('email', sa.String(), nullable=True))



def downgrade() -> None:
    """Downgrade schema."""
    op.drop_column('candidates', 'email')

