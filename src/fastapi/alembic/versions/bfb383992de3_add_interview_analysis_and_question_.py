"""add interview_analysis and question_analysis

Revision ID: bfb383992de3
Revises: 2e5c2317e873
Create Date: 2025-07-15 02:26:08.911634

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bfb383992de3'
down_revision: Union[str, None] = '2e5c2317e873'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('interview_analyses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('interview_session_id', sa.UUID(), nullable=False),
        sa.Column('question_id', sa.String(length=100), nullable=True),
        sa.Column('candidate_answer', sa.Text(), nullable=True),
        sa.Column('ai_analysis', sa.Text(), nullable=True),
        sa.Column('communication_skill', sa.Float(), nullable=True),
        sa.Column('problem_solving_ability', sa.Float(), nullable=True),
        sa.Column('cultural_fit', sa.Float(), nullable=True),
        sa.Column('stress_handling', sa.Float(), nullable=True),
        sa.Column('technical_accuracy', sa.Float(), nullable=True),
        sa.Column('leadership_potential', sa.Float(), nullable=True),
        sa.Column('industry_benchmark', sa.Float(), nullable=True),
        sa.Column('level_comparison', sa.String(length=50), nullable=True),
        sa.Column('strengths', sa.JSON, nullable=True),
        sa.Column('improvements', sa.JSON, nullable=True),
        sa.Column('next_steps', sa.JSON, nullable=True),
        sa.ForeignKeyConstraint(['interview_session_id'], ['interview_sessions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table('interview_analyses')
