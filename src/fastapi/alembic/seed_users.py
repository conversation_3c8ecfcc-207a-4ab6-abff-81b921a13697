from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from app.models.user import User, UserRole
from app.models.agent import Agent
from app.models.candidate import Candidate, EmploymentStatus
from app.models.base import Base
from datetime import datetime
import uuid
from app.core.config import settings

engine = create_engine(settings.DATABASE_URL)
Base.metadata.bind = engine
session = Session(bind=engine)

users_to_seed = [
    {
        "id": uuid.UUID("2337e591-8168-49b8-bc53-a1f11dd30808"),
        "email": "<EMAIL>",
        "password": "admin123",
        "name": "佐藤 健太",
        "role": UserRole.AGENT.value,
        "created_at": datetime.fromisoformat("2025-06-17T23:57:01.940+00:00"),
        "updated_at": datetime.fromisoformat("2025-06-18T00:18:41.756+00:00"),
        "last_login_at": datetime.fromisoformat("2025-06-18T00:18:41.756+00:00"),
        "is_active": True,
        "is_admin": False,
        "organization": "株式会社メンセツ"
    },
    {
        "id": uuid.UUID("6f391c44-ed4c-41b8-9349-39de34bb8670"),
        "email": "<EMAIL>",
        "password": "interview123",
        "name": "鈴木 雅子",
        "role": UserRole.CANDIDATE.value,
        "created_at": datetime.fromisoformat("2025-06-17T23:57:02.172+00:00"),
        "updated_at": datetime.fromisoformat("2025-06-17T23:57:02.172+00:00"),
        "last_login_at": None,
        "is_active": True,
        "is_admin": False,
    }
]

for data in users_to_seed:
    user = session.query(User).filter_by(id=data["id"]).first()

    if user:
        # Update user fields
        for key, value in data.items():
            if key == "password":
                user.password = user.hash_password(value)
            else:
                setattr(user, key, value)
    else:
        # Create new user
        user = User(
            id=data["id"],
            email=data["email"],
            password=User().hash_password(data["password"]),
            name=data["name"],
            role=data["role"],
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
            last_login_at=data.get("last_login_at"),
            is_active=data.get("is_active", True),
            is_admin=data.get("is_admin", False)
        )
        session.add(user)

    # Seed related models
    if data["role"] == UserRole.AGENT.value:
        existing_agent = session.query(Agent).filter_by(user_id=data["id"]).first()
        if not existing_agent:
            agent = Agent(
                id=uuid.uuid4(),
                user_id=data["id"],
                profile_name=data["name"],
                profile_organization=data.get("organization", "")
            )
            session.add(agent)

    elif data["role"] == UserRole.CANDIDATE.value:
        existing_candidate = session.query(Candidate).filter_by(user_id=data["id"]).first()
        if not existing_candidate:
            candidate = Candidate(
                id=uuid.uuid4(),
                user_id=data["id"],
                name_kana=data["name"],
                age=20,
                employment_status=EmploymentStatus.employed,
                company="株式会社メンセツ",
                position="フロントエンドエンジニア",
                address="東京都千代田区永田町1-7-1",
                resume_file="Resume File",
                career_history_file="Career History",
                profile={},
                main_skill="Python",
                other_skill="JavaScript, React, Vue.js",
                experience="3年",
                education="東京大学",
                language="日本語, 英語",
                other_info="フロントエンドエンジニアです。"
            )
            session.add(candidate)

session.commit()
session.close()

print("✅ Seeded users successfully!")
