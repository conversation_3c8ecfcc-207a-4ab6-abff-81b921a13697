from fastapi import APIRouter, Depends, status, HTTPException, UploadFile, File, Form, Query
from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.schemas.candidate import (
    CandidateRegisterRequest, CandidateResponse, CandidateDetailResponse, EmploymentStatus
)
from app.models.base import get_db
from app.services.rag.candidate_rag_service import CandidateRAGService
from app.models.user import User, UserRole
from app.models.schemas.user import UserCreate
from app.models.candidate import Candidate
import logging
from app.services.document.document_service import DocumentService
from uuid import UUID

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post(
    "/register",
    response_model=CandidateResponse,
    status_code=status.HTTP_201_CREATED
)
async def register_candidate(
    candidate_name: str = Form(...),
    candidate_name_kana: str = Form(...),
    candidate_age: int = Form(...),
    candidate_employment_status: EmploymentStatus = Form(...),
    candidate_email: str = Form(...),
    candidate_resume_file: Optional[UploadFile] = File(None),
    candidate_career_history_file: Optional[UploadFile] = File(None),
    candidate_company: Optional[str] = Form(None),
    candidate_position: Optional[str] = Form(None),
    candidate_address: Optional[str] = Form(None),
    candidate_profile: Optional[str] = Form(None),
    candidate_main_skill: Optional[str] = Form(None),
    candidate_other_skill: Optional[str] = Form(None),
    candidate_experience: Optional[str] = Form(None),
    candidate_education: Optional[str] = Form(None),
    candidate_language: Optional[str] = Form(None),
    candidate_other_info: Optional[str] = Form(None),
    cv_file: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db)
):
    document_service = DocumentService()
    
    user_exist = db.query(User).filter(
        User.email == candidate_email,
        User.role == UserRole.CANDIDATE.value
    ).first()

    if user_exist:
        raise HTTPException(status_code=400, detail="Email already exists")
    
    user_data = {
        "email": candidate_email,
        "role": UserRole.CANDIDATE.value,
        "password": "password123",
        "name": candidate_name_kana
    }

    user_instance = User()
    user = user_instance.create_user(db, UserCreate(**user_data))
    
    payload = CandidateRegisterRequest(
        candidate_name=candidate_name,
        candidate_name_kana=candidate_name_kana,
        candidate_age=candidate_age,
        candidate_employment_status=candidate_employment_status,
        candidate_company=candidate_company,
        candidate_position=candidate_position,
        candidate_address=candidate_address,
        # candidate_resume_file=candidate_resume_file,
        # candidate_career_history_file=candidate_career_history_file,
        candidate_profile=candidate_profile,
        candidate_main_skill=candidate_main_skill,
        candidate_other_skill=candidate_other_skill,
        candidate_experience=candidate_experience,
        candidate_education=candidate_education,
        candidate_language=candidate_language,
        candidate_other_info=candidate_other_info,
        candidate_email=candidate_email,
    )

    candidate = await Candidate.create_candidate(db, payload, user.id)
    
    candidate_dict = candidate.__dict__.copy()
    candidate_dict.pop('_sa_instance_state', None)
    
    candidate_rag_service = CandidateRAGService()
    
    if cv_file:
        await document_service.save_candidate_cv_with_rag_sync(
            file=cv_file,
            candidate_id=candidate.id,
            candidate_data=candidate_dict,
            db=db,
            sync_to_rag=True
        )

    if candidate_resume_file:
        await document_service.save_candidate_cv_with_rag_sync(
            file=candidate_resume_file,
            candidate_id=candidate.id,
            candidate_data=candidate_dict,
            db=db,
            sync_to_rag=True
        )
       
    if candidate_career_history_file:
        await document_service.save_candidate_cv_with_rag_sync(
            file=candidate_career_history_file,
            candidate_id=candidate.id,
            candidate_data=candidate_dict,
            db=db,
            sync_to_rag=True
        )

    return CandidateResponse.from_orm(candidate)

@router.get(
    "/",
    response_model=List[CandidateResponse],
    status_code=status.HTTP_200_OK
)
async def list_candidates(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db)
):
    candidates = await Candidate.get_all_candidates(db, skip, limit)
    return [CandidateResponse.from_orm(candidate) for candidate in candidates]

@router.get(
    "/{user_id}",
    response_model=CandidateDetailResponse,
    status_code=status.HTTP_200_OK
)
async def get_candidate_detail(
    user_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    candidate = await Candidate.get_candidate_by_user_id(db, user_id)
    if not candidate:
        raise HTTPException(status_code=404, detail="Candidate not found")
    
    candidate_dict = {
        "id": candidate.id,
        "userId": candidate.user_id,
        "name": candidate.name,
        "nameKana": candidate.name_kana,
        "age": candidate.age,
        "email": candidate.email,
        "employment_status": candidate.employment_status,
        "company": candidate.company,
        "position": candidate.position,
        "address": candidate.address,
        "resumeFile": candidate.resume_file,
        "careerHistoryFile": candidate.career_history_file,
        "profile": candidate.profile,
        "main_skill": candidate.main_skill,
        "other_skill": candidate.other_skill,
        "experience": candidate.experience,
        "education": candidate.education,
        "language": candidate.language,
        "other_info": candidate.other_info,
        "createdAt": candidate.created_at,
        "updatedAt": candidate.updated_at,
        "documents": [
            {
                "id": doc.id,
                "candidateId": doc.candidate_id,
                "fileName": doc.file_name,
                "filePath": doc.file_path,
                "documentType": doc.document_type,
                "fileSize": doc.file_size
            }
            for doc in candidate.documents
        ]
    }
    
    try:
        candidate_rag_service = CandidateRAGService()
        rag_summary = await candidate_rag_service.get_candidate_summary(user_id=user_id)
        
        if rag_summary.get("status") == "error":
            rag_summary = {
                "user_id": str(user_id),
                "status": "error",
                "personal_information": {},
                "skills_and_experience": {},
                "education_and_language": {},
                "documents_info": {},
                "cv_content": {},
                "summary_text": rag_summary.get("message", "No information available")
            }
        
        candidate_dict["ragSummary"] = rag_summary
        
        return candidate_dict
    except Exception as e:
        logger.error(f"Error getting RAG summary for candidate {user_id}: {str(e)}")
        candidate_dict["ragSummary"] = {
            "user_id": str(user_id),
            "status": "error", 
            "personal_information": {},
            "skills_and_experience": {},
            "education_and_language": {},
            "documents_info": {},
            "cv_content": {},
            "summary_text": f"Error: {str(e)}"
        }
        return candidate_dict
