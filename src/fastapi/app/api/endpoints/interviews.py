from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session, joinedload
from app.models.base import get_db
from app.models.interview import InterviewSession
from app.models.company import Company
from app.models.candidate import Candidate
from pydantic import BaseModel
from typing import Dict, List
from uuid import UUID
from app.models.schemas.interview import InterviewResponse as FullInterviewResponse, InterviewLinkCreate, InterviewLinkResponse
from datetime import datetime, timedelta
import os
from fastapi import Response
from app.services.auth.auth_service import AuthService
from app.services.ai.gemini_skill_analysis import GeminiSkillAnalyzer
import asyncio
from sqlalchemy import insert
from app.models.interview import InterviewAnalyses

router = APIRouter()

class InterviewStart(BaseModel):
    interview_id: str

class InterviewResponse(BaseModel):
    id: str
    company_id: str
    candidate_name: str
    status: str
    created_at: str

class InterviewListResponse(BaseModel):
    total_interviews: int
    expired_interviews_count: int
    interviews: List[FullInterviewResponse]

@router.post("/generate-link", response_model=InterviewLinkResponse)
async def generate_interview_link(
    interview_data: InterviewLinkCreate,
    db: Session = Depends(get_db)
):
    """Generate a new interview link for a candidate"""
    # Kiểm tra candidate có tồn tại không
    candidate = db.query(Candidate).options(joinedload(Candidate.user)).filter(Candidate.id == interview_data.candidate_id).first()
    if not candidate:
        raise HTTPException(status_code=404, detail="Candidate not found")

    # Kiểm tra company có tồn tại không
    company = db.query(Company).filter(Company.id == interview_data.company_id).first()
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Tạo phiên phỏng vấn mới
    expires_at = datetime.utcnow() + timedelta(days=interview_data.expiration_days)
    session_token = InterviewSession.generate_session_token()

    interview_session = InterviewSession(
        candidate_id=interview_data.candidate_id,
        company_id=interview_data.company_id,
        status="created",
        session_token=session_token,
        expires_at=expires_at,
        transcript=[],
        feedback=[],
        overall_score=0
    )

    # Thêm metadata nếu có
    metadata = {
        "agent_notes": interview_data.agent_notes,
        "interviewer_role": interview_data.interviewer_role,
        "interview_type": interview_data.interview_type
    }

    # Lưu vào database
    db.add(interview_session)
    db.commit()
    db.refresh(interview_session)

    # Tạo URL phỏng vấn
    candidate_url = os.getenv("NEXT_PUBLIC_CANDIDATE_URL", "http://localhost:3000")
    interview_url = f"{candidate_url}/interview-token?token={session_token}"

    # Lấy tên từ user nếu có, nếu không thì dùng name_kana
    candidate_name = candidate.user.name if candidate.user and candidate.user.name else candidate.name_kana

    return InterviewLinkResponse(
        id=str(interview_session.id),
        session_token=session_token,
        expires_at=expires_at,
        candidate_name=candidate_name,
        interview_url=interview_url
    )

@router.post("/join", response_model=InterviewResponse)
async def join_interview(
    interview_data: InterviewStart, 
    db: Session = Depends(get_db)
):
    """Start a new interview session"""
    interview = InterviewSession.get_interview_by_url(db, interview_data.interview_url)    

    if not interview:
        raise HTTPException(status_code=404, detail="Company not found")

    if interview.status != "created":
        raise HTTPException(status_code=400, detail="Interview already started")

    interview.status = "joined"
    db.commit()

    return InterviewResponse(
        id=str(interview.id),
        company_id=str(interview.company_id),
        candidate_name=interview.candidate_name,
        status=interview.status,
        created_at=interview.created_at.isoformat()
    )

@router.post("/{interview_id}/start", response_model=InterviewResponse)
async def start_interview(
    interview_id: str, 
    db: Session = Depends(get_db)
):
    """Start a new interview session"""
    interview = InterviewSession.get_interview_by_id(db, UUID(interview_id))

    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found")

    if interview.status != "joined":
        raise HTTPException(status_code=400, detail="Interview not joined")

    interview.status = "started"
    db.commit()

    return InterviewResponse(
        id=str(interview.id),
        company_id=str(interview.company_id),
        candidate_name=interview.candidate_name,
        status=interview.status,
        created_at=interview.created_at.isoformat()
    )

@router.get("/", response_model=InterviewListResponse)
async def list_interviews(db: Session = Depends(get_db)):
    """Get list of all interviews, total, and expired count"""
    interviews = db.query(InterviewSession).all()
    now = datetime.utcnow()
    expired_count = sum(1 for interview in interviews if interview.expires_at < now)
    return InterviewListResponse(
        total_interviews=len(interviews),
        expired_interviews_count=expired_count,
        interviews=[
            FullInterviewResponse(
                id=str(interview.id),
                candidate_name=interview.candidate.name_kana,
                candidate_profile=interview.candidate.profile,
                status=interview.status,
                created_at=interview.created_at,
                updated_at=interview.updated_at,
                transcript=interview.transcript,
                feedback=interview.feedback,
                overall_score=interview.overall_score
            )
            for interview in interviews
        ]
    )

@router.get("/{interview_id}/feedback")
async def get_interview_feedback(
    interview_id: str, 
    db: Session = Depends(get_db)
):
    """Get interview feedback"""

    session = db.query(InterviewSession).filter(
        InterviewSession.id == interview_id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Interview session not found")

    if not session.feedback:
        raise HTTPException(status_code=400, detail="Interview not completed yet")

    return {
        "session_id": str(session.id),
        "feedback": session.feedback,
        "overall_score": session.overall_score,
        "status": session.status
    }

@router.get("/token/{token}")
async def get_interview_by_token(
    token: str,
    db: Session = Depends(get_db),
    response: Response = None
):
    """Get interview by token"""
    interview = db.query(InterviewSession).filter(
        InterviewSession.session_token == token
    ).first()

    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found")

    # Kiểm tra xem token có hết hạn không
    if interview.expires_at < datetime.utcnow():
        raise HTTPException(status_code=400, detail="Interview token has expired")

    # Tạo access token cho candidate
    token_data = {
        "sub": str(interview.candidate_id),
        "type": "candidate",
        "interview_id": str(interview.id)
    }

    # Tạo access token với thời hạn 1 ngày
    access_token = AuthService.create_access_token(token_data)

    # Thiết lập cookie cho access token và xóa agent token nếu có
    if response:
        domain = os.getenv("COOKIE_DOMAIN", "localhost")
        # Thiết lập cookie candidate_token
        response.set_cookie(
            key="candidate_token",
            value=access_token,
            httponly=True,
            max_age=24 * 60 * 60,  # 1 day
            domain=domain,
            path="/"
        )

        # Xóa cookie agent_token nếu có
        response.delete_cookie(
            key="agent_token",
            domain=domain,
            path="/"
        )

    return {
        "id": str(interview.id),
        "candidate_id": str(interview.candidate_id),
        "company_id": str(interview.company_id),
        "status": interview.status,
        "expires_at": interview.expires_at.isoformat(),
        "access_token": access_token
    }

@router.get("/candidate/{candidate_id}/sessions")
async def get_candidate_interview_sessions(
    candidate_id: str,
    db: Session = Depends(get_db)
):
    """Get all interview sessions for a candidate"""
    try:
        # Chuyển đổi candidate_id từ string sang UUID
        candidate_uuid = UUID(candidate_id)
        
        # Lấy danh sách phiên phỏng vấn của ứng viên
        sessions = db.query(InterviewSession).filter(
            InterviewSession.candidate_id == candidate_uuid
        ).all()
        
        # Chuyển đổi dữ liệu để trả về
        result = []
        for session in sessions:
            result.append({
                "id": str(session.id),
                "candidate_id": str(session.candidate_id),
                "start_time": session.created_at.isoformat(),
                "end_time": session.updated_at.isoformat() if session.status in ["completed", "ended"] else None,
                "expires_at": session.expires_at.isoformat(),
                "status": session.status,
            })
        
        return result
    except ValueError:
        # Xử lý lỗi khi candidate_id không phải là UUID hợp lệ
        raise HTTPException(status_code=400, detail="Invalid candidate ID format")
    except Exception as e:
        # Xử lý các lỗi khác
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/sessions/{session_id}")
async def get_candidate_interview_session(
    session_id: str,
    db: Session = Depends(get_db)
):
    """Get a specific interview session for a candidate"""
    try:
        session_uuid = UUID(session_id)

        session = db.query(InterviewSession).filter(
            InterviewSession.id == session_uuid
        ).first()

        if not session:
            raise HTTPException(status_code=404, detail="Interview session not found")

        return {
            "id": str(session.id),
            "candidate_id": str(session.candidate_id),
            "candidate_name": session.candidate.name_kana,
            "candidate_email": session.candidate.email,
            "company_id": str(session.company_id),
            "company_name": session.company.name,
            "status": session.status,
            "expires_at": session.expires_at.isoformat(),
            "company_info":  {
                "name": session.company.name,
                "industry": session.company.industry,
                "description": session.company.description,
            }
        }
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid session ID format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/analysis/{interview_session_id}")
async def analyze_interview_session(interview_session_id: str, db: Session = Depends(get_db)):
    """Phân tích kỹ năng ứng viên dựa trên interview_session_id bằng AI Gemini"""
    # Lấy thông tin interview session
    session = db.query(InterviewSession).filter(InterviewSession.id == interview_session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Interview session not found")
    # Lấy thông tin candidate
    candidate = db.query(Candidate).filter(Candidate.id == session.candidate_id).first()
    if not candidate:
        raise HTTPException(status_code=404, detail="Candidate not found")
    # Lấy thông tin company
    company = db.query(Company).filter(Company.id == session.company_id).first()
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")
    # Chuyển đổi dữ liệu sang dict đơn giản
    candidate_data = {
        "id": str(candidate.id),
        "name": candidate.name,
        "email": getattr(candidate, 'email', None),
        "age": getattr(candidate, 'age', None),
        "position": getattr(candidate, 'position', None),
        "profile": getattr(candidate, 'profile', None),
    }
    company_data = {
        "id": str(company.id),
        "name": company.name,
        "industry": getattr(company, 'industry', None),
        "position": getattr(company, 'position', None),
        "description": getattr(company, 'description', None),
    }
    analyzer = GeminiSkillAnalyzer()
    result = await analyzer.analyze(candidate_data, company_data)

    # Lưu vào bảng interview_analyses
    analysis_data = result.get("skills", {})

    analysis = InterviewAnalyses(
        interview_session_id=interview_session_id,
        communication=analysis_data.get("communication"),
        problem_solving=analysis_data.get("problem_solving"),
        cultural_fit=analysis_data.get("company_fit"),
        stress_tolerance=analysis_data.get("stress_tolerance"),
        overall_score=sum([
            analysis_data.get("communication", 0),
            analysis_data.get("problem_solving", 0),
            analysis_data.get("company_fit", 0),
            analysis_data.get("stress_tolerance", 0)
        ]) / 4 if analysis_data else 0,
        benchmark_percent=None,
        level=None
    )
    db.add(analysis)
    db.commit()

    return result
