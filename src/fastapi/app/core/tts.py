from typing import Dict, List, Literal, TypedDict
from dataclasses import dataclass

class TTSConstants:
    """TTS configuration constants"""
    
    # Default voice settings
    DEFAULT_VOICE = "ja-JP-NanamiNeural"
    
    # Supported voices
    SUPPORTED_VOICES = {
        "JAPANESE": {
            "FEMALE": ["ja-JP-Nana<PERSON>Neural", "ja-JP-AoiNeural"],
            "MALE": ["ja-JP-KeitaNeural", "ja-JP-DaichiNeural"]
        },
        "VIETNAMESE": {
            "FEMALE": ["vi-VN-HoaiMyNeural"],
            "MALE": ["vi-VN-NamMinhNeural"]
        }
    }
    
    # Message types
    MESSAGE_TYPES = {
        "REQUEST": "generate_speech",
        "RESPONSE": "tts_response",
        "ERROR": "tts_error"
    }
    
    # Audio settings
    AUDIO = {
        "FORMAT": "mp3",
        "MAX_TEXT_LENGTH": 5000,
        "CACHE_EXPIRY": 30 * 60 * 1000,
        "MAX_CACHE_SIZE": 50,
        "PLAYBACK_TIMEOUT": 10000
    }
    
    # Web Speech API fallback settings
    WEB_SPEECH = {
        "LANG": "ja-JP",
        "RATE": 0.9,
        "PITCH": 1.0,
        "VOLUME": 0.8
    }

# Type definitions for TTS
class TTSRequest(TypedDict):
    type: Literal["generate_speech"]
    text: str
    voice: str

class TTSResponse(TypedDict):
    type: Literal["tts_response", "tts_error"]
    text: str
    voice: str
    format: str
    audio: str
    error: str
    timestamp: str

@dataclass
class VoiceConfig:
    """Voice configuration helper"""
    language: Literal["japanese", "vietnamese"]
    gender: Literal["male", "female"]
    
    def get_voice(self) -> str:
        """Get voice string based on configuration"""
        voices = TTSConstants.SUPPORTED_VOICES
        
        if self.language == "japanese":
            return voices["JAPANESE"]["FEMALE"][0] if self.gender == "female" else voices["JAPANESE"]["MALE"][0]
        else:
            return voices["VIETNAMESE"]["FEMALE"][0] if self.gender == "female" else voices["VIETNAMESE"]["MALE"][0]

# Validation helpers
def validate_tts_request(request: dict) -> bool:
    """Validate TTS request format"""
    if not isinstance(request, dict):
        return False
    
    # Check required fields
    if request.get("type") != TTSConstants.MESSAGE_TYPES["REQUEST"]:
        return False
    
    text = request.get("text", "")
    if not isinstance(text, str) or not text.strip():
        return False
    
    if len(text) > TTSConstants.AUDIO["MAX_TEXT_LENGTH"]:
        return False
    
    # Voice is optional
    voice = request.get("voice")
    if voice is not None and not isinstance(voice, str):
        return False
    
    return True

def validate_tts_response(response: dict) -> bool:
    """Validate TTS response format"""
    if not isinstance(response, dict):
        return False
    
    response_type = response.get("type")
    if response_type not in [TTSConstants.MESSAGE_TYPES["RESPONSE"], TTSConstants.MESSAGE_TYPES["ERROR"]]:
        return False
    
    if "timestamp" not in response:
        return False
    
    return True

def get_voice_config(language: str = "japanese", gender: str = "female") -> str:
    """Get voice configuration string"""
    config = VoiceConfig(
        language=language,  # type: ignore
        gender=gender       # type: ignore
    )
    return config.get_voice()

# Error response helper
def create_tts_error_response(text: str, voice: str, error_message: str, timestamp: str) -> dict:
    """Create standardized TTS error response"""
    return {
        "type": TTSConstants.MESSAGE_TYPES["ERROR"],
        "text": text,
        "voice": voice,
        "error": error_message,
        "timestamp": timestamp
    }

# Success response helper
def create_tts_success_response(text: str, voice: str, audio_base64: str, timestamp: str) -> dict:
    """Create standardized TTS success response"""
    return {
        "type": TTSConstants.MESSAGE_TYPES["RESPONSE"],
        "text": text,
        "voice": voice,
        "audio": audio_base64,
        "format": TTSConstants.AUDIO["FORMAT"],
        "timestamp": timestamp
    }
