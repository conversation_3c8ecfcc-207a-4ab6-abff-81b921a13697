# src/fastapi/app/main.py
import os
from pathlib import Path
from dotenv import load_dotenv

# tìm .env (đặt cạnh Dockerfile hoặc trong repository root)
env_path = Path(__file__).resolve().parents[2] / ".env"
load_dotenv(dotenv_path=env_path, override=False)

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from app.api.api import api_router
from app.api.endpoints.websocket import router as websocket_router
import uvicorn
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi import Request
import logging

app = FastAPI(
    title="Mensetsu-kun API",
    description="AI Interview Tool with Avatar 3D",
    version="1.0.0"
)

def build_origin(url: str) -> str:
    from urllib.parse import urlparse
    if not url:
        return ""
    p = urlparse(url.rstrip('/'))

    return f"{p.scheme}://{p.netloc}"

def get_allowed_origins():
    urls = [
        os.getenv("NEXT_PUBLIC_CANDIDATE_URL"),
        os.getenv("NEXT_PUBLIC_AGENT_URL"),
        os.getenv("NEXT_PUBLIC_BASE_URL"),
    ]

    return list({build_origin(u) for u in urls if u})

app.add_middleware(
    CORSMiddleware,
    allow_origins=get_allowed_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix="/api")
app.include_router(websocket_router)

@app.get("/")
async def root():
    return {"message": "Mensetsu-kun API is running"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "service": "mensetsu-kun-api"
    }

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    errors = []
    for error in exc.errors():
        if error['type'] == 'json_invalid':
            errors.append({
                "field": "request_body",
                "message": "Invalid JSON format. Please check your JSON syntax.",
                "details": error.get('ctx', {}).get('error', 'JSON decode error')
            })
        else:
            errors.append({
                "field": " -> ".join(str(x) for x in error['loc']),
                "message": error['msg'],
                "type": error['type']
            })
    
    return JSONResponse(
        status_code=422,
        content={
            "detail": "Validation error",
            "errors": errors
        }
    )

logging.basicConfig(level=logging.INFO)
logging.getLogger('sqlalchemy.engine').setLevel(logging.ERROR)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
