import json
from langchain_google_genai import ChatGoogleGenerativeAI
from app.core.config import settings

class GeminiSkillAnalyzer:
    def __init__(self):
        self.llm = ChatGoogleGenerativeAI(
            model=settings.GEMINI_MODEL,
            google_api_key=settings.GOOGLE_API_KEY,
            temperature=0.3,
            max_tokens=800
        )

    async def analyze(self, candidate: dict, company: dict) -> dict:
        prompt = self._build_prompt(candidate, company)
        response = await self.llm.ainvoke(prompt)
        return self._parse_response(response.content)

    def _build_prompt(self, candidate: dict, company: dict) -> str:
        base_prompt_template = '''
            あなたはプロの人事AIです。以下の候補者情報と企業情報をもとに、スキル分析と改善提案、さらに3つの面接質問とその模範回答を日本語で行ってください。
            また、それぞれの質問と回答に対して、AIとしての短い分析コメント（AI分析）も付与してください。

            【候補者情報】
            {candidate_info}

            【企業情報】
            {company_info}

            ---

            # 📊 スキル分析
            - コミュニケーション（0-10）
            - 問題解決力（0-10）
            - 企業適合性（0-10）
            - ストレス耐性（0-10）

            # 📈 改善提案
            - 強み（3つ）
            - 改善点（2つ）
            - 次のステップ（1つ）

            # 💬 面接質問と回答
            - 候補者に対して有効な面接質問を3つ作成し、それぞれに模範的な回答例も日本語で記載してください。
            - さらに、各質問と回答のペアごとに、AIとしての短い分析コメント（AI分析）を日本語で記載してください。
            - 参考例:
            {reference_qas}

            ---

            # 出力フォーマット（必ずJSONのみで返答）
            ```json
            {{
            "skills": {{
                "communication": 0,
                "problem_solving": 0,
                "company_fit": 0,
                "stress_tolerance": 0
            }},
            "strengths": ["...", "...", "..."],
            "improvements": ["...", "..."],
            "next_step": "...",
            "interview_questions": [
                {{
                    "question": "...",
                    "answer": "...",
                    "ai_analysis": "..."
                }},
                {{
                    "question": "...",
                    "answer": "...",
                    "ai_analysis": "..."
                }},
                {{
                    "question": "...",
                    "answer": "...",
                    "ai_analysis": "..."
                }}
            ]
            }}
            ```
        '''
        candidate_info = json.dumps(candidate, ensure_ascii=False, indent=2)
        company_info = json.dumps(company, ensure_ascii=False, indent=2)
        reference_qas = [
            {
                "question": "大規模なレガシーシステムのモダナイゼーションプロジェクトを任されました。技術的負債が蓄積した状況で、どのようなアプローチで進めますか？",
                "answer": "レガシーシステムのモダナイゼーションでは、まず現状の技術的負債を詳細に分析し、段階的な移行計画を策定します。ビジネス影響を最小化するため、ストラングラーパターンを採用し...",
                "ai_analysis": "技術的な知見が深く、構造化された回答でした。特にストラングラーパターンの言及は適切で、実務経験の豊富さが感じられます。"
            },
            {
                "question": "質問が見つかりません",
                "answer": "チーム開発においては、まずコードレビューの文化を確立し、ペアプログラミングやモブプログラミングを導入します",
                "ai_analysis": "チーム開発に対する理解が深く、具体的な施策を提示できています。リーダーシップの資質が感じられる回答です。"
            }
        ]
        reference_qas_str = json.dumps(reference_qas, ensure_ascii=False, indent=2)
        return base_prompt_template.format(candidate_info=candidate_info, company_info=company_info, reference_qas=reference_qas_str)

    def _parse_response(self, response: str) -> dict:
        try:
            cleaned = response.strip()
            if cleaned.startswith('```json'):
                cleaned = cleaned[7:-3].strip()
            elif cleaned.startswith('```'):
                cleaned = cleaned[3:-3].strip()
            return json.loads(cleaned)
        except Exception:
            return {"skills": {}, "strengths": [], "improvements": [], "next_step": "", "interview_questions": []} 
