# app/services/interview_service.py
from typing import Dict
import asyncio
import logging
from app.services.websocket.manager import get_websocket_handler

logger = logging.getLogger(__name__)

class InterviewService:
    def __init__(self):
        self._handler = None
        self._flow_interview = None
        self._handle_session = None
        
    @property
    def handler(self):
        """Lazy loading WebSocket handler"""
        if self._handler is None:
            self._handler = get_websocket_handler()
        return self._handler
    
    @property
    def handle_session(self):
        """Lazy loading SessionManager"""
        if self._handle_session is None:
            self._handle_session = self.handler.handle_session
        return self._handle_session
    
    @property
    def flow_interview(self):
        """Lazy loading InterviewFlow"""
        if self._flow_interview is None:
            self._flow_interview = self.handler.flow_interview
        return self._flow_interview

    @property
    def utils(self):
        """Lazy loading WebSocket utils"""
        return self.handler.utils            
    async def communicate(self, session_id: str, data: Dict):
        """
        Main message handler với comprehensive processing
        """
        try:
            await self.handle_session.update_session_data(session_id, {
                "last_activity": asyncio.get_event_loop().time()
            })
            
            message_type = data.get("type", "candidate_message")
            
            if message_type == "candidate_message":
                print(f"Processing candidate message: {data}")
                await self.flow_interview.process_candidate_message(session_id, data)
            # elif message_type == "heartbeat":
            #     await self._handle_heartbeat(session_id)
            # elif message_type == "system_command":
            #     await self._handle_system_command(session_id, data)
            # else:
            #     await self.utils.send_error(session_id, f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling message for {session_id}: {e}")
            await self.utils.send_error(session_id, "has error when handling message. Please try again.")
            
    async def _handle_heartbeat(self, session_id: str) -> None:
        """Handle heartbeat message"""
        await self.handler.utils.send_message(session_id, {
            "type": "pong",
            "timestamp": asyncio.get_event_loop().time()
        })
    
    async def _handle_system_command(self, session_id: str, data: Dict) -> None:
        """Handle system commands"""
        command = data.get("command")
        
        if command == "get_stats":
            stats = self.handler.get_connection_stats()
            await self.handler.utils.send_message(session_id, {
                "type": "system_response",
                "command": "get_stats",
                "data": stats
            })
        else:
            await self.handler.utils.send_error(
                session_id,
                f"Unknown system command: {command}"
            )
