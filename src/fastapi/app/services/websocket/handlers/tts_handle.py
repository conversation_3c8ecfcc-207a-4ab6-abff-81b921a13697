
from typing import Optional
from fastapi import WebSocket
from app.core.error_handling import error_handler, ErrorCode, ErrorContext, ErrorSeverity
from app.core.logging_config import log_performance
from app.services.azure.azure_speech_service import AzureSpeechService
from app.services.websocket.message_dispatcher import message_validator
from datetime import datetime, timezone
import base64
import json
import logging

logger = logging.getLogger(__name__)

class TTSHandler:
    def __init__(self):
        self.azure_speech = AzureSpeechService()

    @log_performance("tts_request", "audio")
    async def handle_tts_request_impl(
        self,
        websocket: WebSocket,
        message: dict,
        session_id: Optional[str] = None
    ) -> bool:
        try:
            if not message_validator.validate_tts_request(message):
                context = ErrorContext(
                    session_id=session_id,
                    operation="tts_validation",
                    component="tts_handler"
                )
                error_info = error_handler.create_error(
                    code=ErrorCode.VALIDATION_PARAMETER_INVALID,
                    message="Invalid TTS request format",
                    context=context
                )
                await error_handler.send_error_to_websocket(websocket, error_info)
                return False

            text = message.get("text", "")
            voice = message.get("voice", "vi-VN-HoaiMyNeural")

            if not text.strip():
                context = ErrorContext(
                    session_id=session_id,
                    operation="tts_validation",
                    component="tts_handler"
                )
                error_info = error_handler.create_error(
                    code=ErrorCode.VALIDATION_PARAMETER_MISSING,
                    message="Text parameter is required for TTS",
                    context=context
                )
                await error_handler.send_error_to_websocket(websocket, error_info)
                return False

            audio_data = await self.azure_speech.text_to_speech(text, voice)
            if not audio_data:
                context = ErrorContext(
                    session_id=session_id,
                    operation="tts_generation",
                    component="azure_speech",
                    additional_data={"text_length": len(text), "voice": voice}
                )
                error_info = error_handler.create_error(
                    code=ErrorCode.AUDIO_TTS_FAILED,
                    message="Azure Speech Service returned no audio data",
                    context=context,
                    severity=ErrorSeverity.HIGH,
                    recovery_suggestions=[
                        "Check Azure Speech Service configuration",
                        "Verify network connectivity",
                        "Try with shorter text"
                    ]
                )
                await error_handler.send_error_to_websocket(websocket, error_info)
                return False

            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            response = {
                "type": "tts_response",
                "text": text,
                "voice": voice,
                "audio": audio_base64,
                "format": "mp3",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            await websocket.send_text(json.dumps(response))

            logger.info(f"TTS generated successfully for session {session_id}", extra={
                "session_id": session_id,
                "text_length": len(text),
                "voice": voice,
                "audio_size": len(audio_data)
            })

            return True

        except Exception as e:
            context = ErrorContext(
                session_id=session_id,
                operation="tts_generation",
                component="tts_handler",
                additional_data={"exception_type": type(e).__name__}
            )

            if "timeout" in str(e).lower():
                code = ErrorCode.NETWORK_TIMEOUT
            elif "azure" in str(e).lower() or "speech" in str(e).lower():
                code = ErrorCode.EXTERNAL_AZURE_SPEECH_FAILED
            else:
                code = ErrorCode.AUDIO_TTS_FAILED

            error_info = error_handler.create_error(
                code=code,
                message=f"TTS generation failed: {str(e)}",
                context=context,
                exception=e,
                severity=ErrorSeverity.HIGH
            )

            await error_handler.send_error_to_websocket(websocket, error_info)
            return False
