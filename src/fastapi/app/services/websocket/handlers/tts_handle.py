
from typing import Optional
from fastapi import WebSocket
from app.core.error_handling import error_handler, <PERSON>rrorCode, ErrorContext, ErrorSeverity
from app.core.logging_config import log_performance
from app.services.azure.azure_speech_service import AzureSpeechService
from app.services.websocket.message_dispatcher import message_validator
from app.core.tts import (
    TTSConstants,
    create_tts_error_response,
    create_tts_success_response
)
from datetime import datetime, timezone
import base64
import json
import logging

logger = logging.getLogger(__name__)

class TTSHandler:
    def __init__(self):
        self.azure_speech = AzureSpeechService()

    @log_performance("tts_request", "audio")
    async def handle_tts_request_impl(
        self,
        websocket: WebSocket,
        message: dict,
        session_id: Optional[str] = None
    ) -> bool:
        try:
            # Validate message structure
            if not message_validator.validate_tts_request(message):
                logger.warning(f"Invalid TTS request format for session {session_id}: {message}")
                context = ErrorContext(
                    session_id=session_id,
                    operation="tts_validation",
                    component="tts_handler"
                )
                error_info = error_handler.create_error(
                    code=ErrorCode.VALIDATION_PARAMETER_INVALID,
                    message="Invalid TTS request format",
                    context=context
                )
                await error_handler.send_error_to_websocket(websocket, error_info)
                return False

            text = message.get("text", "")
            voice = message.get("voice", TTSConstants.DEFAULT_VOICE)

            # Additional validation
            if len(text) > TTSConstants.AUDIO["MAX_TEXT_LENGTH"]:
                logger.warning(f"TTS text too long ({len(text)} chars) for session {session_id}")
                timestamp = datetime.now(timezone.utc).isoformat()
                error_response = create_tts_error_response(
                    text[:100] + "...", voice, f"Text too long (max {TTSConstants.AUDIO['MAX_TEXT_LENGTH']} characters)", timestamp
                )
                await websocket.send_text(json.dumps(error_response))
                return False

            if not text.strip():
                context = ErrorContext(
                    session_id=session_id,
                    operation="tts_validation",
                    component="tts_handler"
                )
                error_info = error_handler.create_error(
                    code=ErrorCode.VALIDATION_PARAMETER_MISSING,
                    message="Text parameter is required for TTS",
                    context=context
                )
                await error_handler.send_error_to_websocket(websocket, error_info)
                return False

            logger.info(f"Generating TTS for text length: {len(text)}, voice: {voice}")
            audio_data = await self.azure_speech.text_to_speech(text, voice)

            if not audio_data:
                logger.error(f"Azure Speech Service returned no audio data for session {session_id}")
                context = ErrorContext(
                    session_id=session_id,
                    operation="tts_generation",
                    component="azure_speech",
                    additional_data={"text_length": len(text), "voice": voice}
                )
                error_info = error_handler.create_error(
                    code=ErrorCode.AUDIO_TTS_FAILED,
                    message="Azure Speech Service returned no audio data",
                    context=context,
                    severity=ErrorSeverity.HIGH,
                    recovery_suggestions=[
                        "Check Azure Speech Service configuration",
                        "Verify network connectivity",
                        "Try with shorter text"
                    ]
                )
                # Send TTS error response in consistent format
                timestamp = datetime.now(timezone.utc).isoformat()
                error_response = create_tts_error_response(
                    text, voice, "Azure Speech Service returned no audio data", timestamp
                )
                await websocket.send_text(json.dumps(error_response))
                await error_handler.send_error_to_websocket(websocket, error_info)
                return False

            logger.info(f"Audio data generated successfully, size: {len(audio_data)} bytes")

            # Encode audio data to base64
            try:
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                logger.info(f"Audio encoded to base64, length: {len(audio_base64)}")
            except Exception as encode_error:
                logger.error(f"Failed to encode audio to base64: {encode_error}")
                timestamp = datetime.now(timezone.utc).isoformat()
                error_response = create_tts_error_response(
                    text, voice, f"Failed to encode audio: {str(encode_error)}", timestamp
                )
                await websocket.send_text(json.dumps(error_response))
                return False

            timestamp = datetime.now(timezone.utc).isoformat()
            response = create_tts_success_response(text, voice, audio_base64, timestamp)

            await websocket.send_text(json.dumps(response))
            logger.info(f"TTS response sent successfully for session {session_id}")

            logger.info(f"TTS generated successfully for session {session_id}", extra={
                "session_id": session_id,
                "text_length": len(text),
                "voice": voice,
                "audio_size": len(audio_data)
            })

            return True

        except Exception as e:
            context = ErrorContext(
                session_id=session_id,
                operation="tts_generation",
                component="tts_handler",
                additional_data={"exception_type": type(e).__name__}
            )

            if "timeout" in str(e).lower():
                code = ErrorCode.NETWORK_TIMEOUT
            elif "azure" in str(e).lower() or "speech" in str(e).lower():
                code = ErrorCode.EXTERNAL_AZURE_SPEECH_FAILED
            else:
                code = ErrorCode.AUDIO_TTS_FAILED

            error_info = error_handler.create_error(
                code=code,
                message=f"TTS generation failed: {str(e)}",
                context=context,
                exception=e,
                severity=ErrorSeverity.HIGH
            )

            # Send TTS error response in consistent format
            timestamp = datetime.now(timezone.utc).isoformat()
            error_response = create_tts_error_response(
                message.get("text", ""),
                message.get("voice", TTSConstants.DEFAULT_VOICE),
                f"TTS generation failed: {str(e)}",
                timestamp
            )
            await websocket.send_text(json.dumps(error_response))
            await error_handler.send_error_to_websocket(websocket, error_info)
            return False
