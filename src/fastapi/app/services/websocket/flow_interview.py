# app/services/websocket/flow_interview.py

import json
from datetime import datetime
import logging
from typing import Dict, Optional
import asyncio
from app.services.websocket.handle_session import InterviewSessionManager
from app.services.rag_service import RAGService
from app.services.websocket.protocols import MessageUtilsProtocol
from app.services.ai.message_classifier import GeminiMessageClassifier
from app.services.ai.interview_orchestrator import InterviewOrchestrator

logger = logging.getLogger(__name__)

class InterviewFlow:
    def __init__(self, message_utils: Optional[MessageUtilsProtocol] = None):
        self.handle_session = InterviewSessionManager()
        self._message_utils = message_utils
        self.gemini_classifier = GeminiMessageClassifier()
        self.rag_service = RAGService()
        self.interview_orchestrator = InterviewOrchestrator()
        
    def set_message_utils(self, message_utils: MessageUtilsProtocol) -> None:
        self._message_utils = message_utils
    
    @property
    def utils(self) -> MessageUtilsProtocol:
        if not self._message_utils:
            raise RuntimeError(
                "MessageUtils not injected. Call set_message_utils() first."
            )
        return self._message_utils
        
    async def process_candidate_message(self, session_id: str, data: Dict):
        """
        Process candidate's response with AI analysis
        """
        message_content = data.get("message", data.get("text", "")).strip()
        print(f"Message content: {message_content}")
        if not message_content:
            await self.utils.send_error(session_id, "Message cannot be empty.")
            return

        await self.handle_session.update_session_data(session_id, {
            "last_activity": asyncio.get_event_loop().time()
        })
        
        await self.utils.send_message(session_id, {
            "type": "processing_status",
            "message": "Processing your response..."
        })
        
        session_data = await self.handle_session.get_session_data(session_id)
        conversation_history = session_data.get("conversation_history", [])
        
        classification_result = await self.gemini_classifier.classify_message(
            message_content, 
            conversation_history,
            session_id
        )
        
        await self._update_candidate_profile(session_id, classification_result)
        
        company_context = await self.rag_service.retrieve_contextual_information(
            company_id=session_data["company_id"],
            query_context={
                "current_stage": session_data["current_stage"],
                "skills_detected": session_data["skills_detected"],
                "message_type": classification_result["message_type"],
                "conversation_length": len(session_data["conversation_history"])
            }       
        )
        
        await self.utils.send_message(session_id, {
            "type": "processing_status",
            "message": "Preparing next question..."
        })
        
        next_question_data = await self.interview_orchestrator.generate_next_question(
            session_data=session_data,
            classification_result=classification_result,
            company_context=company_context,
            current_message=message_content
        )
        
        session_data["conversation_history"].append({
            "question": session_data.get("last_question", ""),
            "answer": message_content,
            "classification": classification_result,
            "timestamp": asyncio.get_event_loop().time()
        })
        
        session_data["last_question"] = next_question_data["question"]
        session_data["question_count"] += 1
        session_data["current_stage"] = next_question_data.get("stage", session_data["current_stage"])
        
        await self.utils.send_message(session_id, {
            "type": "interview_question",
            "question": next_question_data["question"],
            "stage": next_question_data.get("stage"),
            "question_type": next_question_data.get("type"),
            "analysis_feedback": self.generate_immediate_feedback(classification_result),
            "progress": {
                "question_count": session_data["question_count"],
                "estimated_remaining": next_question_data.get("estimated_remaining", 5)
            }
        })
            
    async def _update_candidate_profile(self, session_id: str, classification_result: Dict):
        """
        Update candidate profile based on AI analysis
        """
        session = await self.handle_session.get_session_data(session_id)
        extracted_info = classification_result.get("extracted_info", {})
        
        new_skills = extracted_info.get("skills_mentioned", [])
        session["skills_detected"].extend(new_skills)
        
        if extracted_info.get("experience_years"):
            session["candidate_profile"]["experience_years"] = extracted_info["experience_years"]
        
        industries = extracted_info.get("industries_mentioned", [])
        if industries:
            current_industries = session["candidate_profile"].get("industries", [])
            session["candidate_profile"]["industries"] = list(set(current_industries + industries))
        
        soft_skills = extracted_info.get("soft_skills", [])
        if soft_skills:
            current_soft_skills = session["candidate_profile"].get("soft_skills", [])
            session["candidate_profile"]["soft_skills"] = list(set(current_soft_skills + soft_skills))
                
    def generate_immediate_feedback(self, classification_result: Dict) -> Dict:
        """
        Generate immediate feedback for candidate
        """
        feedback = {
            "clarity_score": classification_result.get("clarity_score", 0.7),
            "relevance_score": classification_result.get("relevance_score", 0.6),
            "sentiment": classification_result.get("sentiment", "neutral")
        }
        
        if feedback["clarity_score"] > 0.8:
            feedback["message"] = "Câu trả lời rất rõ ràng và chi tiết!"
        elif feedback["clarity_score"] > 0.6:
            feedback["message"] = "Câu trả lời tốt, bạn có thể thêm ví dụ cụ thể."
        else:
            feedback["message"] = "Hãy thử trả lời chi tiết hơn với ví dụ cụ thể."
        
        return feedback

    
    