# fastapi/app/models/interview.py
from sqlalchemy import Column, String, Text, DateTime, JSON, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.base import Base
import uuid
from datetime import datetime, timedelta
from app.models.schemas.interview import Interview<PERSON><PERSON>
from sqlalchemy.orm import Session
import logging
import string
import secrets
from typing import Dict
from app.models.video_analysis import VideoAnalysis, VideoSegment

logger = logging.getLogger(__name__)

class InterviewSession(Base):
    __tablename__ = "interview_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    candidate_id = Column(UUID(as_uuid=True), ForeignKey("candidates.id", ondelete="CASCADE"), nullable=False)
    company_id = Column(UUID(as_uuid=True), ForeignKey("companies.id", ondelete="CASCADE"), nullable=False)
    status = Column(String(50), default="created", index=True)
    transcript = Column(JSON, default=list)
    feedback = Column(JSON, default=list)
    overall_score = Column(Integer, default=0)
    session_token = Column(String(255), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    company = relationship("Company", back_populates="interviews")
    candidate = relationship("Candidate", back_populates="interviews")
    
    # Add relationships
    video_segments = relationship("VideoSegment", back_populates="interview_session", cascade="all, delete-orphan")
    video_analyses = relationship("VideoAnalysis", back_populates="interview_session", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<InterviewSession(id={self.id}, candidate={self.candidate.name}, status={self.status})>"
    
    @classmethod
    def create_interview(cls, db: Session, interview_data, company_id: UUID):
        """Create new interview"""
        logger.info(f"Creating interview for company {interview_data}")
        db_interview = cls(
            company_id=company_id,
            candidate_id=interview_data.candidate_id,
            status="created",
            session_token=cls.generate_session_token(),
            expires_at=datetime.utcnow() + timedelta(hours=1),
            transcript=[],
            feedback=[],
            overall_score=0,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(db_interview)
        db.commit()
        db.refresh(db_interview)
        return db_interview
    
    @classmethod
    def get_interview_by_id(cls, db: Session, interview_id: UUID):
        """Get interview by ID"""
        return db.query(cls).filter(cls.id == interview_id).first()
    
    @staticmethod
    def generate_session_token() -> str:
        """Generate unique session token"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(32))
    
    def get_video_analysis_summary(self, db: Session) -> Dict:
        """Get summary of all video analyses for this session"""
        analyses = VideoAnalysis.get_by_session(db, self.id)
        
        if not analyses:
            return {"total_analyses": 0, "average_scores": {}, "overall_insights": []}
        
        # Calculate average scores
        total_scores = {}
        for analysis in analyses:
            for key, value in analysis.scores.items():
                if key not in total_scores:
                    total_scores[key] = []
                total_scores[key].append(value)
        
        average_scores = {
            key: sum(values) / len(values) 
            for key, values in total_scores.items()
        }
        
        # Collect insights
        all_insights = []
        for analysis in analyses:
            if analysis.behavioral_insights:
                all_insights.extend(analysis.behavioral_insights.get("key_observations", []))
        
        return {
            "total_analyses": len(analyses),
            "average_scores": average_scores,
            "overall_insights": all_insights[:10],  # Top 10 insights
            "analyses_by_question": [
                {
                    "question_id": str(analysis.question_id),
                    "scores": analysis.scores,
                    "confidence": analysis.confidence_score
                }
                for analysis in analyses
            ]
        }

class InterviewAnalyses(Base):
    __tablename__ = "interview_analyses"

    id = Column(Integer, primary_key=True, autoincrement=True)
    interview_session_id = Column(UUID(as_uuid=True), ForeignKey("interview_sessions.id", ondelete="CASCADE"), nullable=False, unique=True)
    communication = Column(Integer, nullable=False)
    problem_solving = Column(Integer, nullable=False)
    cultural_fit = Column(Integer, nullable=False)
    stress_tolerance = Column(Integer, nullable=False)
    overall_score = Column(Integer, nullable=False)
    benchmark_percent = Column(Integer, nullable=True)
    level = Column(String(50), nullable=True)

    interview_session = relationship("InterviewSession", backref="interview_analysis", uselist=False)