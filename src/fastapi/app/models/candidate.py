# fastapi/app/models/candidate.py
from datetime import datetime
from sqlalchemy import <PERSON>olean, Column, DateTime, Enum, Integer, JSON, String, ForeignKey
from app.models.base import Base
import enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.orm import Session
from app.models.schemas.candidate import CandidateRegisterRequest
import uuid

class EmploymentStatus(str, enum.Enum):
    employed = "employed"
    unemployed = "unemployed"

class Candidate(Base):
    __tablename__ = "candidates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False, index=True)
    user = relationship("User", back_populates="candidate_profile")

    name = Column(String, nullable=True)
    name_kana = Column(String, nullable=False)
    age = Column(Integer, nullable=False)
    email = Column(String, nullable=True)
    employment_status = Column(String(20), nullable=False)  # "employed", "unemployed"
    company = Column(String, nullable=True)
    position = Column(String, nullable=True)
    address = Column(String, nullable=True)
    resume_file = Column(String, default=True)
    career_history_file = Column(String, default=True)
    profile = Column(JSON, nullable=True)
    main_skill = Column(String, nullable=True)
    other_skill = Column(String, nullable=True)
    experience = Column(String, nullable=True)
    education = Column(String, nullable=True)
    language = Column(String, nullable=True)
    other_info = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    interviews = relationship(
        "InterviewSession",
        back_populates="candidate",
        cascade="all, delete-orphan"
    )
    
    documents = relationship(
        "CandidateDocument", back_populates="candidate", cascade="all, delete-orphan", lazy="select")

    def __repr__(self):
        return f"<Candidate(id={self.id}, user_id={self.user_id})>"
    
    @staticmethod
    async def create_candidate(db: Session, payload: CandidateRegisterRequest, user_id: UUID):
        candidate = Candidate(
            user_id=user_id,
            name=payload.candidate_name,
            name_kana=payload.candidate_name_kana,
            age=payload.candidate_age,
            employment_status=payload.candidate_employment_status,
            company=payload.candidate_company,
            position=payload.candidate_position,
            address=payload.candidate_address,
            # resume_file=payload.candidate_resume_file,
            # career_history_file=payload.candidate_career_history_file,
            profile=payload.candidate_profile,
            main_skill=payload.candidate_main_skill,
            other_skill=payload.candidate_other_skill,
            experience=payload.candidate_experience,
            education=payload.candidate_education,
            language=payload.candidate_language,
            other_info=payload.candidate_other_info,
            email=payload.candidate_email,
        )

        db.add(candidate)
        db.commit()
        db.refresh(candidate)
        return candidate
    
    @staticmethod
    async def get_all_candidates(db: Session, skip: int, limit: int):
        candidates = db.query(Candidate).order_by(Candidate.created_at.desc()).offset(skip).limit(limit).all()
        return candidates
    
    @staticmethod
    async def get_candidate_by_user_id(db: Session, user_id: UUID):
        candidate = db.query(Candidate).filter(Candidate.user_id == user_id).first()
        return candidate
    
class CandidateDocument(Base):
    __tablename__ = "candidate_documents"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    candidate_id = Column(UUID(as_uuid=True), ForeignKey("candidates.id"), nullable=False)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    document_type = Column(String(50), nullable=False)  # "resume", "cv", "portfolio", "certificate"
    file_size = Column(Integer)
    mime_type = Column(String(100))
    upload_date = Column(DateTime, default=datetime.utcnow)
    processed = Column(Boolean, default=False)
    text_chunks_count = Column(Integer, default=0)
    
    candidate = relationship("Candidate", back_populates="documents")