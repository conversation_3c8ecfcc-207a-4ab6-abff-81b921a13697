/**
 * 求職者プロファイル関連の型定義
 * AI面接官の個人化された質問生成とデータ期限管理に使用
 */

// ===== 基本情報 =====

/**
 * 求職者の基本個人情報
 */
export interface PersonalInfo {
  name: string;
  email: string;
  phone?: string;
  location?: string;
  nationality?: string;
  birthDate?: string; // プライバシー考慮のためオプショナル
  currentPosition?: string; // 現在の職位
  yearsOfExperience?: number; // 経験年数
  mainSkill?: string;
  otherSkill?: string;
  experience?: string;
}

/**
 * 学歴情報
 */
export interface Education {
  id: string;
  institution: string;        // 学校名
  degree: string;            // 学位・学科
  field?: string;            // 専攻分野
  startDate: string;
  endDate?: string;         // 在学中の場合はnull
  gpa?: number;             // GPA（オプショナル）
  achievements?: string[];   // 成績優秀者、表彰など
  isActive: boolean;        // 現在在学中かどうか
}

/**
 * 職歴情報
 */
export interface WorkExperience {
  id: string;
  company: string;
  position: string;
  department?: string;
  startDate: string;
  endDate?: string;         // 現職の場合はnull
  isCurrentRole: boolean;   // 現職かどうか
  responsibilities: string[]; // 担当業務
  achievements: string[];   // 実績・成果
  skills: string[];         // 使用技術・スキル
  teamSize?: number;        // チーム規模
  budgetManaged?: number;   // 管理予算（百万円単位）
}

/**
 * スキル情報
 */
export interface SkillInfo {
  name: string;
  category: 'technical' | 'business' | 'language' | 'certification' | 'soft';
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  yearsOfExperience?: number;
  lastUsed?: string;        // 最後に使用した日付
  certificationDate?: string; // 資格取得日
  validUntil?: string;      // 資格有効期限
}

/**
 * 資格・認定情報
 */
export interface Certification {
  id: string;
  name: string;
  issuingOrganization: string;
  issueDate: string;
  expirationDate?: string;
  credentialId?: string;
  credentialUrl?: string;
}

// ===== 履歴書関連 =====

/**
 * アップロードされた履歴書ファイル情報
 */
export interface ResumeFile {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: 'pdf' | 'doc' | 'docx';
  uploadedAt: string;
  filePath: string;         // ストレージパス
  isProcessed: boolean;     // 解析済みかどうか
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  processingError?: string;
}

/**
 * 解析された履歴書コンテンツ
 */
export interface ParsedResumeContent {
  summary?: string;         // 自己紹介・サマリー
  objective?: string;       // 志望動機・目標
  skills: SkillInfo[];
  experience: WorkExperience[];
  education: Education[];
  certifications: Certification[];
  achievements: string[];   // 特筆すべき成果
  keywords: string[];       // 抽出されたキーワード
  languages: {
    name: string;
    level: 'native' | 'fluent' | 'conversational' | 'basic';
  }[];
  
  // 解析メタデータ
  extractionConfidence: number; // 抽出精度 (0-1)
  extractedAt: string;
  extractionEngine: string; // 使用した解析エンジン
}

// ===== 求職者プロファイル =====

/**
 * 求職者の経験レベル・キャリア情報
 */
export interface CareerProfile {
  level: 'entry' | 'mid' | 'senior' | 'executive' | 'c-level';
  totalYearsOfExperience: number;
  currentRole?: string;
  currentCompany?: string;
  previousRoles: WorkExperience[];
  primarySkills: string[];          // 主要スキル
  industries: string[];             // 経験業界
  functionAreas: string[];          // 職能領域（営業、開発、マーケティング等）
  managementExperience?: {
    hasExperience: boolean;
    teamSizeManaged: number;
    budgetManaged?: number;
    yearsInManagement: number;
  };
}

/**
 * 求職活動関連情報
 */
export interface JobSearchPreferences {
  targetIndustries: string[];
  targetRoles: string[];
  targetCompanies?: string[];
  salaryExpectation?: {
    min: number;
    max: number;
    currency: string;
  };
  locationPreferences: string[];
  workArrangement: 'onsite' | 'remote' | 'hybrid' | 'flexible';
  availabilityDate?: string;        // 入社可能日
  currentJobSearchStatus: 'active' | 'passive' | 'not_looking';
}

/**
 * 完全な求職者プロファイル
 */
export interface CandidateProfile {
  id: string;
  personalInfo: PersonalInfo;
  resume?: {
    file: ResumeFile;
    parsedContent?: ParsedResumeContent;
  };
  career?: CareerProfile;
  jobSearchPreferences?: JobSearchPreferences;
  
  // 簡易版用の追加フィールド
  workExperience?: WorkExperience[];
  skills?: SkillInfo[];
  education?: Education[];
  preferences?: {
    communicationStyle: 'casual' | 'balanced' | 'formal';
    feedbackPreference: 'constructive' | 'direct' | 'encouraging';
    pacePreference: 'slow' | 'normal' | 'fast';
  };
  resumeData?: {
    personalInfo?: Partial<PersonalInfo>;
    workExperience?: WorkExperience[];
    skills?: string[];
    education?: Education[];
  };
  metadata?: {
    createdAt: string;
    updatedAt: string;
    source: 'manual_input' | 'resume_upload' | 'api_import';
    version?: number;
  };
  
  // AI分析用情報
  aiAnalysis?: {
    strengthsIdentified: string[];
    improvementAreas: string[];
    recommendedQuestionCategories: string[];
    interviewReadinessScore: number; // 0-100
    lastAnalyzedAt: string;
  };
  
  // メタデータ
  createdAt: string;
  updatedAt: string;
  createdBy: string;            // エージェントID
  dataSource: 'manual' | 'resume_upload' | 'api_import';
  
  // プライバシー・期限管理
  privacySettings?: {
    dataRetentionPeriod: number;  // 日数
    allowDataSharing: boolean;
    allowAnalytics: boolean;
  };
  expirationDate?: string;       // データ削除予定日
  isActive?: boolean;
}

// ===== データ期限管理 =====

/**
 * 拡張されたリンク期限管理
 */
export interface LinkExpirationLogic {
  linkId: string;
  candidateProfileId: string;
  
  // 各種期限設定
  baseExpiration: string;                    // 基本設定期限
  agentUpdatedExpiration?: string;           // エージェント延長期限  
  longestCompanyExpiration?: string;         // 他企業の最長期限
  candidateProfileExpiration: string;        // プロファイル期限
  
  // 計算結果
  effectiveExpiration: string;               // 実際の適用期限（最大値）
  
  // 期限延長履歴
  extensionHistory: {
    extendedAt: string;
    extendedBy: string;                      // エージェントID
    previousExpiration: string;
    newExpiration: string;
    reason?: string;
  }[];
  
  // 通知設定
  notificationSettings: {
    notifyDaysBefore: number[];              // 何日前に通知するか
    lastNotificationSent?: string;
    notificationsSent: string[];
  };
}

/**
 * 候補者プロファイルのライフサイクル管理
 */
export interface ProfileLifecycleStatus {
  profileId: string;
  currentStatus: 'active' | 'expiring_soon' | 'expired' | 'archived' | 'deleted';
  
  // 期限情報
  createdAt: string;
  expiresAt: string;
  lastAccessedAt?: string;
  lastUpdatedAt: string;
  
  // 削除関連
  scheduledDeletionAt?: string;
  deletionReason?: 'expired' | 'user_request' | 'privacy_policy' | 'manual';
  
  // 関連データ
  linkedInterviewSessions: string[];         // 関連する面接セッションID
  linkedCompanies: string[];                 // 練習した企業ID
  
  // 同意管理
  consentStatus: {
    dataProcessingConsent: boolean;
    dataRetentionConsent: boolean;
    consentGivenAt: string;
    consentUpdatedAt?: string;
  };
}

// ===== API関連型定義 =====

/**
 * 履歴書解析リクエスト
 */
export interface ResumeParsingRequest {
  fileId: string;
  candidateId?: string;
  parsingOptions: {
    extractSkills: boolean;
    extractExperience: boolean;
    extractEducation: boolean;
    extractCertifications: boolean;
    language: 'ja' | 'en';
    confidenceThreshold: number;
  };
}

/**
 * 履歴書解析レスポンス
 */
export interface ResumeParsingResponse {
  success: boolean;
  parsedContent?: ParsedResumeContent;
  errors?: string[];
  warnings?: string[];
  processingTime: number;       // 処理時間（ミリ秒）
  confidence: number;           // 全体的な解析信頼度
}

/**
 * 候補者プロファイル作成リクエスト
 */
export interface CreateCandidateProfileRequest {
  personalInfo: Partial<PersonalInfo>;
  resumeFileId?: string;
  jobSearchPreferences?: Partial<JobSearchPreferences>;
  privacySettings?: Partial<CandidateProfile['privacySettings']>;
  manualCareerInfo?: Partial<CareerProfile>;
}

/**
 * 候補者プロファイル検索・フィルタリング
 */
export interface CandidateProfileSearchOptions {
  query?: string;               // 名前、スキル、企業名での検索
  experienceLevel?: CareerProfile['level'][];
  industries?: string[];
  skills?: string[];
  createdAfter?: string;
  createdBefore?: string;
  isActive?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'name' | 'experienceLevel';
  sortOrder?: 'asc' | 'desc';
}

export default CandidateProfile;