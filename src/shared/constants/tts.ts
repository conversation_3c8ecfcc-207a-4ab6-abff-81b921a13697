export const TTS_CONSTANTS = {
    DEFAULT_VOICE: "ja-<PERSON><PERSON><PERSON><PERSON>Neural",
    
    // Supported voices
    SUPPORTED_VOICES: {
        JAPANESE: {
            FEMALE: ["ja-<PERSON>-<PERSON><PERSON>Neural", "ja-<PERSON><PERSON><PERSON><PERSON>Neur<PERSON>"],
            MALE: ["ja-<PERSON><PERSON><PERSON><PERSON>eural", "ja-<PERSON><PERSON>DaichiNeural"]
        },
        VIETNAMESE: {
            FEMALE: ["vi-VN-HoaiMyNeural"],
            MALE: ["vi-VN-NamMinhNeural"]
        }
    },
    
    // Message types
    MESSAGE_TYPES: {
        REQUEST: "generate_speech",
        RESPONSE: "tts_response",
        ERROR: "tts_error"
    },
    
    // Audio settings
    AUDIO: {
        FORMAT: "mp3",
        MAX_TEXT_LENGTH: 5000,
        CACHE_EXPIRY: 30 * 60 * 1000, // 30 minutes
        MAX_CACHE_SIZE: 50,
        PLAYBACK_TIMEOUT: 10000 // 10 seconds
    },
    
    // Web Speech API fallback settings
    WEB_SPEECH: {
        LANG: "ja-<PERSON>",
        RATE: 0.9,
        PITCH: 1.0,
        VOLUME: 0.8
    }
} as const;

// Type definitions for TTS
export interface TTSRequest {
    type: typeof TTS_CONSTANTS.MESSAGE_TYPES.REQUEST;
    text: string;
    voice?: string;
}

export interface TTSResponse {
    type: typeof TTS_CONSTANTS.MESSAGE_TYPES.RESPONSE | typeof TTS_CONSTANTS.MESSAGE_TYPES.ERROR;
    text?: string;
    voice?: string;
    format?: string;
    audio?: string;
    error?: string;
    timestamp: string;
}

// Voice configuration helper
export function getVoiceConfig(language: 'japanese' | 'vietnamese' = 'japanese', gender: 'male' | 'female' = 'female'): string {
    const voices = TTS_CONSTANTS.SUPPORTED_VOICES;
    
    if (language === 'japanese') {
        return gender === 'female' ? voices.JAPANESE.FEMALE[0] : voices.JAPANESE.MALE[0];
    } else {
        return gender === 'female' ? voices.VIETNAMESE.FEMALE[0] : voices.VIETNAMESE.MALE[0];
    }
}

// Validation helpers
export function validateTTSRequest(request: any): request is TTSRequest {
    return (
        typeof request === 'object' &&
        request.type === TTS_CONSTANTS.MESSAGE_TYPES.REQUEST &&
        typeof request.text === 'string' &&
        request.text.trim().length > 0 &&
        request.text.length <= TTS_CONSTANTS.AUDIO.MAX_TEXT_LENGTH &&
        (request.voice === undefined || typeof request.voice === 'string')
    );
}

export function validateTTSResponse(response: any): response is TTSResponse {
    return (
        typeof response === 'object' &&
        (response.type === TTS_CONSTANTS.MESSAGE_TYPES.RESPONSE || response.type === TTS_CONSTANTS.MESSAGE_TYPES.ERROR) &&
        typeof response.timestamp === 'string'
    );
}
