import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

import { GoogleMeetInterview, CompanyInfo } from '../components/MeetingLayout';
import { InterviewProvider } from '../contexts/InterviewContext';
import { InterviewSessionService } from '../services/interviewSessionService';

interface InterviewData {
  id: string;
  candidate_id: string;
  candidate_name: string;
  expires_at: string;
  status: string;
  company_info: CompanyInfo;
}

const FocusedInterview: React.FC = () => {
  const [interviewData, setInterviewData] = useState<InterviewData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { sessionId } = router.query;

  const interviewSessionService = new InterviewSessionService();

  useEffect(() => {
    if (!sessionId) return;

    const fetchInterviewData = async () => {
      try {
        setLoading(true);
        setError(null);

        const sessionDetail =
          await interviewSessionService.getInterviewSessionDetail(
            sessionId as string
          );

        const transformedData: InterviewData = {
          id: sessionDetail.id,
          candidate_id: sessionDetail.candidate_id,
          candidate_name: sessionDetail.candidate_name,
          status: sessionDetail.status,
          expires_at: sessionDetail.expires_at,
          company_info: sessionDetail.company_info || [],
        };

        setInterviewData(transformedData);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : 'Failed to load interview session'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchInterviewData();
  }, [sessionId]);

  const handleComplete = (results: any[]) => {
    if (!interviewData) return;

    // const result = {
    //   id: `result_${Date.now()}`,
    //   linkId: sessionId as string,
    //   scenarioId: interviewData.scenarioId,
    //   companyName: interviewData.companyInfo.name,
    //   position: interviewData.companyInfo.position,
    //   practiceDate: new Date().toISOString(),
    //   candidateName: 'Test User',
    //   candidateEmail: '<EMAIL>',
    //   overallScore: 8.5,
    //   communicationScore: 8.8,
    //   technicalScore: 8.2,
    //   duration: 45,
    //   feedback: results,
    // };

    // router.push(`/interview-result?resultId=${result.id}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading interview session...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500 text-lg">Error: {error}</div>
      </div>
    );
  }

  return interviewData ? (
    <InterviewProvider>
      <GoogleMeetInterview
        scenarioId={sessionId as string}
        companyInfo={interviewData.company_info}
        agentNotes={[]}
        onComplete={handleComplete}
        onBack={() => router.back()}
      />
    </InterviewProvider>
  ) : null;
};

export default FocusedInterview;
