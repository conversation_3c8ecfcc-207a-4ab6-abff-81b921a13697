import React from 'react';
import {
  <PERSON>,
  Container,
  VStack,
  <PERSON><PERSON>,
  Text,
  Spinner,
  Center,
  Alert,
  AlertIcon,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { InterviewSessionService } from '../../services/interviewSessionService';

interface InterviewSession {
  id: string;
  candidate_id: string;
  start_time: string;
  end_time: string;
  status: string;
}

interface InterviewToken {
  id: string;
  candidate_id: string;
  company_id: string;
  status: string;
  expires_at: string;
  access_token: string;
}

const InterviewPage = () => {
  const router = useRouter();
  const { interviewId } = router.query;
  const toast = useToast();
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [sessions, setSessions] = React.useState<InterviewSession[]>([]);

  // <PERSON><PERSON>m để thiết lập cookie
  const setCookie = (name: string, value: string, days: number, domain: string = '') => {
    const expires = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toUTCString();
    const domainPart = domain ? `; domain=${domain}` : '';
    document.cookie = `${name}=${value}; expires=${expires}; path=/${domainPart}; SameSite=Lax`;
  };

  // Hàm để xóa cookie
  const deleteCookie = (name: string, domain: string = '') => {
    const domainPart = domain ? `; domain=${domain}` : '';
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/${domainPart}`;
  };

  React.useEffect(() => {
    const initializeInterview = async () => {
      if (!interviewId) return; // Wait for router to be ready

      try {
        setIsLoading(true);

        const interviewSessionService = new InterviewSessionService();

        // 1. Lấy thông tin token
        const tokenData = await interviewSessionService.getInterviewToken(String(interviewId));
        console.log('tokenData: ', tokenData);

        // Nếu có access_token, lưu vào cookie và chuyển hướng đến demo-login
        if (tokenData && tokenData.access_token) {
          // Lưu access token vào cookie
          const domain = process.env.NEXT_PUBLIC_COOKIE_DOMAIN || '.localhost';
          setCookie('candidate_token', tokenData.access_token, 1, domain);
          setCookie('candidate_id', tokenData.candidate_id, 1, domain);

          // Xóa cookie agent nếu có
          deleteCookie('access_token', domain);

          // Lưu trạng thái tab vào localStorage để demo-login biết cần kích hoạt tab nào
          localStorage.setItem('active_tab', '1'); // 1 là index của tab 候補者テスト機能

          // Chuyển hướng đến trang demo-login
          router.push('/demo-login');
          return;
        }

        // // 2. Lấy danh sách sessions của candidate
        // const sessionData = await interviewService.getInterviewSessions(tokenData.candidate_id);
        // // setSessions(sessionData);
        // console.log('sessionData: ', sessionData);

      } catch (err) {
        console.error('Error initializing interview:', err);
        setError('面接の初期化中にエラーが発生しました。');
        toast({
          title: 'エラー',
          description: '面接の初期化に失敗しました。',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    initializeInterview();
  }, [interviewId, toast, router]);

  if (isLoading) {
    return (
      <Center minH="100vh">
        <VStack spacing={4}>
          <Spinner size="xl" color="blue.500" thickness="4px" />
          <Text>面接を準備中...</Text>
        </VStack>
      </Center>
    );
  }

  if (error) {
    return (
      <Container maxW="container.md" py={8}>
        <Alert status="error">
          <AlertIcon />
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Box minH="100vh" bg="gray.50">
      <Container maxW="container.xl" py={8}>
        <VStack spacing={6} align="stretch">
          <Heading size="lg">面接ルーム</Heading>
          <Text>面接ID: {interviewId}</Text>
          
          {sessions.length > 0 ? (
            <Box overflowX="auto">
              <Heading size="md" mb={2}>セッション履歴</Heading>
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th>セッションID</Th>
                    <Th>開始時間</Th>
                    <Th>終了時間</Th>
                    <Th>ステータス</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {sessions.map((session) => (
                    <Tr key={session.id}>
                      <Td>{session.id}</Td>
                      <Td>{new Date(session.start_time).toLocaleString('ja-JP')}</Td>
                      <Td>{session.end_time ? new Date(session.end_time).toLocaleString('ja-JP') : '-'}</Td>
                      <Td>{session.status}</Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </Box>
          ) : (
            <Alert status="info">
              <AlertIcon />
              セッション履歴がありません。
            </Alert>
          )}
          
          {/* TODO: Add interview components here */}
          {/* Example:
          <InterviewRoom />
          <QuestionDisplay />
          <AnswerInput />
          */}
        </VStack>
      </Container>
    </Box>
  );
}

export default InterviewPage; 