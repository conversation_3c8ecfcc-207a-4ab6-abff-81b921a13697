// TTS Service - Clean and optimized
import { TTS_CONSTANTS, TTSRequest, TTSResponse, validateTTSResponse } from '../../shared/constants/tts';

export class TTSService {
    private audioCache = new Map<string, { data: string; timestamp: number }>();
    private useWebSpeechFallback = false;
    private readonly maxCacheSize = TTS_CONSTANTS.AUDIO.MAX_CACHE_SIZE;
    private readonly cacheExpiry = TTS_CONSTANTS.AUDIO.CACHE_EXPIRY;
    private currentAudio: HTMLAudioElement | null = null;

    constructor(private sendMessage: (message: any) => boolean) { }

    async requestSpeech(text: string, voice: string = TTS_CONSTANTS.DEFAULT_VOICE): Promise<void> {
        if (!text?.trim()) return;

        this.cleanExpiredCache();

        const cacheKey = `${text}_${voice}`;
        const cached = this.audioCache.get(cacheKey);

        if (cached && !this.useWebSpeechFallback) {
            this.playAudio(cached.data);
            return;
        }

        if (this.useWebSpeechFallback) {
            this.useWebSpeechAPI(text);
            return;
        }

        const request: TTSRequest = {
            type: TTS_CONSTANTS.MESSAGE_TYPES.REQUEST,
            text,
            voice
        };

        const success = this.sendMessage(request);

        if (!success) {
            this.switchToWebSpeechFallback(text);
        }
    }

    handleTTSResponse(response: TTSResponse): void {
        // Validate response format
        if (!validateTTSResponse(response)) {
            console.error('❌ Invalid TTS response format:', response);
            return;
        }

        console.log('🎵 TTS Response received:', {
            type: response.type,
            text: response.text?.substring(0, 50) + '...',
            voice: response.voice,
            audioLength: response.audio?.length || 0,
            format: response.format,
            timestamp: response.timestamp
        });

        if (response.type === TTS_CONSTANTS.MESSAGE_TYPES.RESPONSE && response.audio) {
            console.log('✅ Audio data received, length:', response.audio.length);
            this.cacheAudio(response.text || '', response.voice || '', response.audio);
            this.playAudio(response.audio);
        } else if (response.type === TTS_CONSTANTS.MESSAGE_TYPES.ERROR) {
            console.error('❌ TTS Error:', response.error);
            this.switchToWebSpeechFallback(response.text || '');
        }
    }

    private playAudio(audioBase64: string): void {
        console.log('🔊 Starting audio playback, base64 length:', audioBase64.length);

        try {
            // Stop current audio if playing
            if (this.currentAudio) {
                this.currentAudio.pause();
                this.currentAudio.src = '';
                this.currentAudio = null;
            }

            // Validate base64 data
            if (!audioBase64 || audioBase64.length === 0) {
                console.error('❌ Empty audio data received');
                this.switchToWebSpeechFallback();
                return;
            }

            // Decode base64 with better error handling
            let audioData: string;
            try {
                audioData = atob(audioBase64);
            } catch (decodeError) {
                console.error('❌ Base64 decode failed:', decodeError);
                this.switchToWebSpeechFallback();
                return;
            }

            console.log('Decoded audio data length:', audioData.length);

            if (audioData.length === 0) {
                console.error('❌ Decoded audio data is empty');
                this.switchToWebSpeechFallback();
                return;
            }

            const audioArray = new Uint8Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                audioArray[i] = audioData.charCodeAt(i);
            }

            // Create blob with proper MIME type
            const audioBlob = new Blob([audioArray], { type: 'audio/mpeg' });
            console.log('Created MP3 blob, size:', audioBlob.size, 'bytes');

            if (audioBlob.size === 0) {
                console.error('❌ Created blob is empty');
                this.switchToWebSpeechFallback();
                return;
            }

            const audioUrl = URL.createObjectURL(audioBlob);
            console.log('🔗 Created audio URL:', audioUrl);

            const audio = new Audio();
            this.currentAudio = audio;

            // Set up event handlers before setting src
            audio.onloadstart = () => {
                console.log('🔄 Audio loading started');
            };

            audio.oncanplay = () => {
                console.log('✅ Audio can play, starting playback');
                audio.play().catch((playError) => {
                    console.error('❌ Audio play failed:', playError);
                    this.currentAudio = null;
                    this.cleanup(audioUrl);
                    this.switchToWebSpeechFallback();
                });
            };

            audio.onerror = (error) => {
                console.error('❌ Audio error event:', error);
                console.error('❌ Audio error details:', {
                    error: error,
                    audioSrc: audio.src,
                    audioReadyState: audio.readyState,
                    audioNetworkState: audio.networkState,
                    blobSize: audioBlob.size,
                    blobType: audioBlob.type,
                    audioError: audio.error
                });
                this.currentAudio = null;
                this.cleanup(audioUrl);
                this.switchToWebSpeechFallback();
            };

            audio.onended = () => {
                console.log('✅ Audio playback ended');
                this.currentAudio = null;
                this.cleanup(audioUrl);
            };

            audio.onabort = () => {
                console.log('⚠️ Audio playback aborted');
                this.currentAudio = null;
                this.cleanup(audioUrl);
            };

            // Set source after event handlers are set up
            audio.src = audioUrl;
            audio.load(); // Explicitly load the audio
            console.log('🎧 Audio element created and loading');

            // Cleanup after timeout as fallback
            setTimeout(() => {
                if (this.currentAudio === audio) {
                    this.cleanup(audioUrl);
                    this.currentAudio = null;
                }
            }, TTS_CONSTANTS.AUDIO.PLAYBACK_TIMEOUT);

        } catch (error) {
            console.error('❌ Audio playback failed:', error);
            this.switchToWebSpeechFallback();
        }
    }

    private useWebSpeechAPI(text: string): void {
        if (!('speechSynthesis' in window) || !text) {
            console.error('Web Speech API is not available');
            return;
        }

        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = TTS_CONSTANTS.WEB_SPEECH.LANG;
        utterance.rate = TTS_CONSTANTS.WEB_SPEECH.RATE;
        utterance.pitch = TTS_CONSTANTS.WEB_SPEECH.PITCH;
        utterance.volume = TTS_CONSTANTS.WEB_SPEECH.VOLUME;

        const voices = speechSynthesis.getVoices();
        const japaneseVoice = voices.find(voice =>
            voice.lang.startsWith('ja') || voice.lang.includes('JP')
        );

        if (japaneseVoice) {
            utterance.voice = japaneseVoice;
        }

        speechSynthesis.speak(utterance);
    }

    private cacheAudio(text: string, voice: string, audio: string): void {
        const cacheKey = `${text}_${voice}`;

        if (this.audioCache.size >= this.maxCacheSize) {
            const oldestKey = this.audioCache.keys().next().value;
            if (oldestKey) {
                this.audioCache.delete(oldestKey);
            }
        }

        this.audioCache.set(cacheKey, {
            data: audio,
            timestamp: Date.now()
        });
    }

    private cleanExpiredCache(): void {
        const now = Date.now();
        for (const [key, value] of this.audioCache.entries()) {
            if (now - value.timestamp > this.cacheExpiry) {
                this.audioCache.delete(key);
            }
        }
    }

    private switchToWebSpeechFallback(text?: string): void {
        console.log('🔄 Switching to Web Speech API fallback');
        this.useWebSpeechFallback = true;
        if (text?.trim()) {
            this.useWebSpeechAPI(text.trim());
        }
    }

    private cleanup(audioUrl: string): void {
        try {
            URL.revokeObjectURL(audioUrl);
            console.log('🧹 Audio URL cleaned up');
        } catch (error) {
            console.warn('⚠️ Failed to cleanup audio URL:', error);
        }
    }

    clearCache(): void {
        this.audioCache.clear();
        this.useWebSpeechFallback = false;
    }

    getCacheSize(): number {
        return this.audioCache.size;
    }

    stopCurrentAudio(): void {
        if (this.currentAudio) {
            this.currentAudio.pause();
            this.currentAudio = null;
        }
    }

    isPlaying(): boolean {
        return this.currentAudio !== null && !this.currentAudio.paused;
    }

    resetService(): void {
        console.log('🔄 Resetting TTS service');
        this.stopCurrentAudio();
        this.useWebSpeechFallback = false;
        this.clearCache();
    }

    getServiceStatus(): {
        isPlaying: boolean;
        useWebSpeech: boolean;
        cacheSize: number;
        hasCurrentAudio: boolean;
    } {
        return {
            isPlaying: this.isPlaying(),
            useWebSpeech: this.useWebSpeechFallback,
            cacheSize: this.getCacheSize(),
            hasCurrentAudio: this.currentAudio !== null
        };
    }
}
