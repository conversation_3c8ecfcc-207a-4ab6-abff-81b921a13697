// TTS Service - Clean and optimized
import { TTS_CONSTANTS, TTSRequest, TTSResponse, validateTTSResponse } from '../../shared/constants/tts';

export class TTSService {
    private audioCache = new Map<string, { data: string; timestamp: number }>();
    private useWebSpeechFallback = false;
    private readonly maxCacheSize = TTS_CONSTANTS.AUDIO.MAX_CACHE_SIZE;
    private readonly cacheExpiry = TTS_CONSTANTS.AUDIO.CACHE_EXPIRY;
    private currentAudio: HTMLAudioElement | null = null;

    constructor(private sendMessage: (message: any) => boolean) { }

    async requestSpeech(text: string, voice: string = TTS_CONSTANTS.DEFAULT_VOICE): Promise<void> {
        if (!text?.trim()) return;

        this.cleanExpiredCache();

        const cacheKey = `${text}_${voice}`;
        const cached = this.audioCache.get(cacheKey);

        if (cached && !this.useWebSpeechFallback) {
            this.playAudio(cached.data);
            return;
        }

        if (this.useWebSpeechFallback) {
            this.useWebSpeechAPI(text);
            return;
        }

        const request: TTSRequest = {
            type: TTS_CONSTANTS.MESSAGE_TYPES.REQUEST,
            text,
            voice
        };

        const success = this.sendMessage(request);

        if (!success) {
            this.switchToWebSpeechFallback(text);
        }
    }

    handleTTSResponse(response: TTSResponse): void {
        // Validate response format
        if (!validateTTSResponse(response)) {
            console.error('❌ Invalid TTS response format:', response);
            return;
        }

        console.log('🎵 TTS Response received:', {
            type: response.type,
            text: response.text?.substring(0, 50) + '...',
            voice: response.voice,
            audioLength: response.audio?.length || 0,
            format: response.format,
            timestamp: response.timestamp
        });

        if (response.type === TTS_CONSTANTS.MESSAGE_TYPES.RESPONSE && response.audio) {
            console.log('✅ Audio data received, length:', response.audio.length);

            // Debug audio data
            this.debugAudioData(response.audio);

            this.cacheAudio(response.text || '', response.voice || '', response.audio);
            this.playAudio(response.audio);
        } else if (response.type === TTS_CONSTANTS.MESSAGE_TYPES.ERROR) {
            console.error('❌ TTS Error:', response.error);
            this.switchToWebSpeechFallback(response.text || '');
        }
    }

    private playAudio(audioBase64: string): void {
        console.log('🔊 Starting audio playback, base64 length:', audioBase64.length);

        try {
            // Stop current audio if playing
            if (this.currentAudio) {
                this.currentAudio.pause();
                this.currentAudio.src = '';
                this.currentAudio = null;
            }

            // Validate base64 data
            if (!audioBase64 || audioBase64.length === 0) {
                console.error('❌ Empty audio data received');
                this.switchToWebSpeechFallback();
                return;
            }

            // Check minimum length for valid audio
            if (audioBase64.length < 100) {
                console.error('❌ Audio data too short:', audioBase64.length);
                this.switchToWebSpeechFallback();
                return;
            }

            // Validate base64 format
            if (!/^[A-Za-z0-9+/]*={0,2}$/.test(audioBase64)) {
                console.error('❌ Invalid base64 format');
                this.switchToWebSpeechFallback();
                return;
            }

            // Decode base64 with better error handling
            let audioData: string;
            try {
                audioData = atob(audioBase64);
            } catch (decodeError) {
                console.error('❌ Base64 decode failed:', decodeError);
                this.switchToWebSpeechFallback();
                return;
            }

            console.log('Decoded audio data length:', audioData.length);

            if (audioData.length === 0) {
                console.error('❌ Decoded audio data is empty');
                this.switchToWebSpeechFallback();
                return;
            }

            // Check if it looks like valid audio data (MP3 should start with ID3 or sync frame)
            const firstBytes = audioData.substring(0, 3);
            const isValidMP3 = firstBytes === 'ID3' ||
                              (audioData.charCodeAt(0) === 0xFF && (audioData.charCodeAt(1) & 0xE0) === 0xE0);

            if (!isValidMP3) {
                console.warn('⚠️ Audio data may not be valid MP3 format, first bytes:',
                    Array.from(firstBytes).map(c => c.charCodeAt(0).toString(16)).join(' '));

                // If audio data looks completely invalid (all zeros or very small), fallback immediately
                const isAllZeros = audioData.substring(0, 100).split('').every(c => c.charCodeAt(0) === 0);
                if (isAllZeros || audioData.length < 1000) {
                    console.error('❌ Audio data appears to be invalid (all zeros or too small)');
                    this.switchToWebSpeechFallback();
                    return;
                }
            }

            const audioArray = new Uint8Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                audioArray[i] = audioData.charCodeAt(i);
            }

            // Try multiple MIME types for better compatibility
            const mimeTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav'];
            let audioBlob: Blob | null = null;

            for (const mimeType of mimeTypes) {
                audioBlob = new Blob([audioArray], { type: mimeType });
                console.log(`Created blob with ${mimeType}, size: ${audioBlob.size} bytes`);

                if (audioBlob.size > 0) {
                    break;
                }
            }

            if (!audioBlob || audioBlob.size === 0) {
                console.error('❌ Failed to create valid blob');
                this.switchToWebSpeechFallback();
                return;
            }

            const audioUrl = URL.createObjectURL(audioBlob);
            console.log('🔗 Created audio URL:', audioUrl);

            const audio = new Audio();
            this.currentAudio = audio;

            // Set audio properties for better compatibility
            audio.preload = 'auto';
            audio.crossOrigin = null; // Remove CORS restrictions for blob URLs

            let hasStartedPlaying = false;

            // Set up event handlers before setting src
            audio.onloadstart = () => {
                console.log('🔄 Audio loading started');
            };

            audio.onloadeddata = () => {
                console.log('📊 Audio data loaded');
            };

            audio.oncanplay = () => {
                console.log('✅ Audio can play, starting playback');
                if (!hasStartedPlaying) {
                    hasStartedPlaying = true;
                    audio.play().catch((playError) => {
                        console.error('❌ Audio play failed:', playError);
                        this.currentAudio = null;
                        this.cleanup(audioUrl);
                        this.switchToWebSpeechFallback();
                    });
                }
            };

            audio.onplaying = () => {
                console.log('🎵 Audio is playing');
            };

            audio.onerror = (error) => {
                console.error('❌ Audio error event:', error);

                const errorDetails = {
                    error: error,
                    audioSrc: audio.src,
                    audioReadyState: audio.readyState,
                    audioNetworkState: audio.networkState,
                    blobSize: audioBlob.size,
                    blobType: audioBlob.type,
                    audioError: audio.error,
                    audioErrorCode: audio.error?.code,
                    audioErrorMessage: audio.error?.message
                };

                console.error('❌ Audio error details:', errorDetails);

                // Try to get more specific error info
                if (audio.error) {
                    switch (audio.error.code) {
                        case MediaError.MEDIA_ERR_ABORTED:
                            console.error('❌ Audio error: MEDIA_ERR_ABORTED');
                            break;
                        case MediaError.MEDIA_ERR_NETWORK:
                            console.error('❌ Audio error: MEDIA_ERR_NETWORK');
                            break;
                        case MediaError.MEDIA_ERR_DECODE:
                            console.error('❌ Audio error: MEDIA_ERR_DECODE - Invalid audio format');
                            break;
                        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
                            console.error('❌ Audio error: MEDIA_ERR_SRC_NOT_SUPPORTED');
                            break;
                    }
                }

                this.currentAudio = null;
                this.cleanup(audioUrl);
                this.switchToWebSpeechFallback();
            };

            audio.onended = () => {
                console.log('✅ Audio playback ended');
                this.currentAudio = null;
                this.cleanup(audioUrl);
            };

            audio.onabort = () => {
                console.log('⚠️ Audio playback aborted');
                this.currentAudio = null;
                this.cleanup(audioUrl);
            };

            // Set source after event handlers are set up
            audio.src = audioUrl;

            // Try to load the audio
            try {
                audio.load();
                console.log('🎧 Audio element created and loading');
            } catch (loadError) {
                console.error('❌ Failed to load audio:', loadError);
                this.currentAudio = null;
                this.cleanup(audioUrl);
                this.switchToWebSpeechFallback();
                return;
            }

            // Cleanup after timeout as fallback
            setTimeout(() => {
                if (this.currentAudio === audio) {
                    this.cleanup(audioUrl);
                    this.currentAudio = null;
                }
            }, TTS_CONSTANTS.AUDIO.PLAYBACK_TIMEOUT);

        } catch (error) {
            console.error('❌ Audio playback failed:', error);
            this.switchToWebSpeechFallback();
        }
    }

    private useWebSpeechAPI(text: string): void {
        if (!('speechSynthesis' in window) || !text) {
            console.error('❌ Web Speech API is not available');
            return;
        }

        console.log('🗣️ Using Web Speech API for:', text.substring(0, 50) + '...');

        // Cancel any ongoing speech
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = TTS_CONSTANTS.WEB_SPEECH.LANG;
        utterance.rate = TTS_CONSTANTS.WEB_SPEECH.RATE;
        utterance.pitch = TTS_CONSTANTS.WEB_SPEECH.PITCH;
        utterance.volume = TTS_CONSTANTS.WEB_SPEECH.VOLUME;

        // Wait for voices to be loaded
        const setVoiceAndSpeak = () => {
            const voices = speechSynthesis.getVoices();
            console.log('🎤 Available voices:', voices.length);

            const japaneseVoice = voices.find(voice =>
                voice.lang.startsWith('ja') || voice.lang.includes('JP')
            );

            if (japaneseVoice) {
                utterance.voice = japaneseVoice;
                console.log('✅ Using Japanese voice:', japaneseVoice.name);
            } else {
                console.warn('⚠️ No Japanese voice found, using default');
            }

            // Add event listeners
            utterance.onstart = () => {
                console.log('🗣️ Web Speech started');
            };

            utterance.onend = () => {
                console.log('✅ Web Speech ended');
            };

            utterance.onerror = (error) => {
                console.error('❌ Web Speech error:', error);
            };

            speechSynthesis.speak(utterance);
        };

        // Check if voices are already loaded
        if (speechSynthesis.getVoices().length > 0) {
            setVoiceAndSpeak();
        } else {
            // Wait for voices to be loaded
            speechSynthesis.onvoiceschanged = setVoiceAndSpeak;
        }
    }

    private cacheAudio(text: string, voice: string, audio: string): void {
        const cacheKey = `${text}_${voice}`;

        if (this.audioCache.size >= this.maxCacheSize) {
            const oldestKey = this.audioCache.keys().next().value;
            if (oldestKey) {
                this.audioCache.delete(oldestKey);
            }
        }

        this.audioCache.set(cacheKey, {
            data: audio,
            timestamp: Date.now()
        });
    }

    private cleanExpiredCache(): void {
        const now = Date.now();
        for (const [key, value] of this.audioCache.entries()) {
            if (now - value.timestamp > this.cacheExpiry) {
                this.audioCache.delete(key);
            }
        }
    }

    private switchToWebSpeechFallback(text?: string): void {
        console.log('🔄 Switching to Web Speech API fallback');

        // Check if Web Speech API is available
        if (!('speechSynthesis' in window)) {
            console.error('❌ Web Speech API not available, cannot fallback');
            return;
        }

        this.useWebSpeechFallback = true;
        if (text?.trim()) {
            this.useWebSpeechAPI(text.trim());
        }
    }

    private cleanup(audioUrl: string): void {
        try {
            URL.revokeObjectURL(audioUrl);
            console.log('🧹 Audio URL cleaned up');
        } catch (error) {
            console.warn('⚠️ Failed to cleanup audio URL:', error);
        }
    }

    clearCache(): void {
        this.audioCache.clear();
        this.useWebSpeechFallback = false;
    }

    getCacheSize(): number {
        return this.audioCache.size;
    }

    stopCurrentAudio(): void {
        if (this.currentAudio) {
            this.currentAudio.pause();
            this.currentAudio = null;
        }
    }

    isPlaying(): boolean {
        return this.currentAudio !== null && !this.currentAudio.paused;
    }

    resetService(): void {
        console.log('🔄 Resetting TTS service');
        this.stopCurrentAudio();
        this.useWebSpeechFallback = false;
        this.clearCache();
    }

    getServiceStatus(): {
        isPlaying: boolean;
        useWebSpeech: boolean;
        cacheSize: number;
        hasCurrentAudio: boolean;
        audioSupport: { [key: string]: string };
    } {
        return {
            isPlaying: this.isPlaying(),
            useWebSpeech: this.useWebSpeechFallback,
            cacheSize: this.getCacheSize(),
            hasCurrentAudio: this.currentAudio !== null,
            audioSupport: this.checkAudioSupport()
        };
    }

    private checkAudioSupport(): { [key: string]: string } {
        const audio = new Audio();
        return {
            mp3: audio.canPlayType('audio/mpeg') || 'not supported',
            wav: audio.canPlayType('audio/wav') || 'not supported',
            ogg: audio.canPlayType('audio/ogg') || 'not supported',
            m4a: audio.canPlayType('audio/mp4') || 'not supported'
        };
    }

    // Debug method to test audio data
    debugAudioData(audioBase64: string): void {
        console.log('🔍 Debugging audio data...');
        console.log('Base64 length:', audioBase64.length);
        console.log('First 100 chars:', audioBase64.substring(0, 100));
        console.log('Last 100 chars:', audioBase64.substring(audioBase64.length - 100));

        try {
            const audioData = atob(audioBase64);
            console.log('Decoded length:', audioData.length);

            // Check first few bytes
            const firstBytes = [];
            for (let i = 0; i < Math.min(10, audioData.length); i++) {
                firstBytes.push(audioData.charCodeAt(i).toString(16).padStart(2, '0'));
            }
            console.log('First bytes (hex):', firstBytes.join(' '));

            // Check if it's MP3
            const isMP3 = audioData.substring(0, 3) === 'ID3' ||
                         (audioData.charCodeAt(0) === 0xFF && (audioData.charCodeAt(1) & 0xE0) === 0xE0);
            console.log('Appears to be MP3:', isMP3);

        } catch (error) {
            console.error('Debug failed:', error);
        }
    }
}
