const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8080/api';

interface InterviewTokenResponse {
  candidate_id: string;
  interview_id: string;
  status: string;
  access_token: string; // Thêm property này
  // Thêm các trường kh<PERSON>c nếu cần
}

export interface InterviewSession {
  id: string;
  candidate_id: string;
  start_time: string;
  end_time: string;
  status: string;
  // Thêm các trường khác nếu cần
}

export class InterviewSessionService {
  /**
   * Lấy thông tin interview bằng token
   * @param token - Session token từ URL
   */
  async getInterviewToken(token: string): Promise<InterviewTokenResponse> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/interviews/token/${token}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include", // Để nhận cookie từ backend
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || "Failed to get interview token");
      }
      return response.json();
    } catch (error) {
      console.error("Failed to get interview token:", error);
      throw error;
    }
  }

  /**
   * Lấy danh sách phiên phỏng vấn của ứng viên
   */
  async getInterviewSessions(candidateId: string): Promise<InterviewSession[]> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/interviews/candidate/${candidateId}/sessions`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include", // Để gửi cookie nếu có
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || "Failed to get interview sessions");
      }
      return response.json();
    } catch (error) {
      console.error("Failed to get interview sessions:", error);
      return [];
    }
  }

  /**
   * Bắt đầu phiên phỏng vấn mới
   */
  async startInterviewSession(interviewId: string) {
    try {
      const response = await fetch(
        `${API_BASE_URL}/interviews/${interviewId}/start`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include", // Để gửi cookie nếu có
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || "Failed to start interview session");
      }
      return response.json();
    } catch (error) {
      console.error("Failed to start interview session:", error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết phiên phỏng vấn
   * @param sessionId - ID của phiên phỏng vấn
   */
  async getInterviewSessionDetail(sessionId: string): Promise<any> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/interviews/sessions/${sessionId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include", // Để gửi cookie nếu có
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || "Failed to get interview session detail");
      }
      return response.json();
    } catch (error) {
      console.error("Failed to get interview session detail:", error);
      throw error;
    }
  }

};