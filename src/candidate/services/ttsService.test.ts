// TTS Service Test - For debugging audio issues
import { TTSService } from './ttsService';
import { TTS_CONSTANTS } from '../../shared/constants/tts';

// Mock WebSocket message sender
const mockSendMessage = (message: any): boolean => {
    console.log('📤 Mock sending message:', message);
    return true;
};

// Test TTS Service
export function testTTSService() {
    console.log('🧪 Starting TTS Service tests...');
    
    const ttsService = new TTSService(mockSendMessage);
    
    // Test 1: Service initialization
    console.log('Test 1: Service Status');
    console.log(ttsService.getServiceStatus());
    
    // Test 2: Request speech
    console.log('Test 2: Request Speech');
    ttsService.requestSpeech('こんにちは、テストです。');
    
    // Test 3: Handle mock response
    console.log('Test 3: Handle TTS Response');
    const mockResponse = {
        type: TTS_CONSTANTS.MESSAGE_TYPES.RESPONSE,
        text: 'こんにちは、テストです。',
        voice: TTS_CONSTANTS.DEFAULT_VOICE,
        audio: 'dGVzdCBhdWRpbyBkYXRh', // base64 for "test audio data"
        format: TTS_CONSTANTS.AUDIO.FORMAT,
        timestamp: new Date().toISOString()
    };
    
    ttsService.handleTTSResponse(mockResponse);
    
    // Test 4: Handle error response
    console.log('Test 4: Handle TTS Error');
    const mockErrorResponse = {
        type: TTS_CONSTANTS.MESSAGE_TYPES.ERROR,
        text: 'エラーテスト',
        voice: TTS_CONSTANTS.DEFAULT_VOICE,
        error: 'Test error message',
        timestamp: new Date().toISOString()
    };
    
    ttsService.handleTTSResponse(mockErrorResponse);
    
    // Test 5: Cache functionality
    console.log('Test 5: Cache Size');
    console.log('Cache size:', ttsService.getCacheSize());
    
    // Test 6: Reset service
    console.log('Test 6: Reset Service');
    ttsService.resetService();
    console.log('Status after reset:', ttsService.getServiceStatus());
    
    console.log('✅ TTS Service tests completed');
}

// Audio debugging helper
export function debugAudioPlayback(audioBase64: string) {
    console.log('🔍 Debugging audio playback...');
    
    try {
        // Test base64 decoding
        const audioData = atob(audioBase64);
        console.log('✅ Base64 decode successful, length:', audioData.length);
        
        // Test Uint8Array creation
        const audioArray = new Uint8Array(audioData.length);
        for (let i = 0; i < audioData.length; i++) {
            audioArray[i] = audioData.charCodeAt(i);
        }
        console.log('✅ Uint8Array created, length:', audioArray.length);
        
        // Test blob creation
        const audioBlob = new Blob([audioArray], { type: 'audio/mpeg' });
        console.log('✅ Blob created, size:', audioBlob.size, 'type:', audioBlob.type);
        
        // Test URL creation
        const audioUrl = URL.createObjectURL(audioBlob);
        console.log('✅ URL created:', audioUrl);
        
        // Test audio element
        const audio = new Audio(audioUrl);
        console.log('✅ Audio element created');
        
        // Test loading
        audio.onloadstart = () => console.log('🔄 Loading started');
        audio.oncanplay = () => console.log('✅ Can play');
        audio.onerror = (error) => console.error('❌ Audio error:', error);
        
        audio.load();
        
        // Cleanup
        setTimeout(() => {
            URL.revokeObjectURL(audioUrl);
            console.log('🧹 URL cleaned up');
        }, 5000);
        
    } catch (error) {
        console.error('❌ Debug failed:', error);
    }
}

// Browser compatibility check
export function checkAudioSupport() {
    console.log('🔍 Checking audio support...');
    
    const audio = new Audio();
    
    const formats = {
        mp3: 'audio/mpeg',
        wav: 'audio/wav',
        ogg: 'audio/ogg',
        m4a: 'audio/mp4'
    };
    
    for (const [format, mimeType] of Object.entries(formats)) {
        const canPlay = audio.canPlayType(mimeType);
        console.log(`${format.toUpperCase()}: ${canPlay || 'not supported'}`);
    }
    
    // Check Web Speech API
    if ('speechSynthesis' in window) {
        console.log('✅ Web Speech API supported');
        const voices = speechSynthesis.getVoices();
        console.log(`Available voices: ${voices.length}`);
        const japaneseVoices = voices.filter(v => v.lang.includes('ja'));
        console.log(`Japanese voices: ${japaneseVoices.length}`);
    } else {
        console.log('❌ Web Speech API not supported');
    }
}

// Export for global access in browser console
if (typeof window !== 'undefined') {
    (window as any).ttsDebug = {
        testTTSService,
        debugAudioPlayback,
        checkAudioSupport
    };
}
