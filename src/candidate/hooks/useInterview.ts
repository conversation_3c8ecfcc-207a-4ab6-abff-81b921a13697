import { useState } from 'react';
import type { InterviewSession } from '../services/interviewService';
import type { CandidateProfile } from '../types/evaluation';

interface UseInterviewReturn {
  candidate: CandidateProfile | null;
  setCandidate: (candidate: CandidateProfile | null) => void;
  sessions: InterviewSession[];
  setSessions: (sessions: InterviewSession[]) => void;
}

export const useInterview = (): UseInterviewReturn => {
  const [candidate, setCandidate] = useState<CandidateProfile | null>(null);
  const [sessions, setSessions] = useState<InterviewSession[]>([]);

  return {
    candidate,
    setCandidate,
    sessions,
    setSessions,
  };
};

export default useInterview;
