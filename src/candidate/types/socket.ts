export interface SocketMessage {
  type: string;
  timestamp: string;
  [key: string]: any;
}

export interface SystemMessage extends SocketMessage {
  type: 'system_message';
  message: string;
}

export interface ErrorMessage extends SocketMessage {
  type: 'error';
  message: string;
}

export interface PingMessage extends SocketMessage {
  type: 'ping';
}

export interface AnswerSubmittedMessage extends SocketMessage {
  type: 'answer_submitted';
  question_id: string;
  question_text: string;
}

export interface ResponseAnswerMessage extends SocketMessage {
  type: 'candidate_message';
  question_id: string;
  text?: string;
  audio_data?: string;
  confidence?: number;
  method: 'speech_to_text' | 'text_input' | 'mixed';
  audio_format?: 'webm' | 'wav' | 'mp3';
  duration?: number;
}