import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  Box,
  Flex,
  Text,
  useBreakpointValue,
  Badge,
  Tooltip,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaVideoSlash,
  FaMicrophoneSlash,
  FaExclamationTriangle,
  FaCheckCircle,
  FaCamera,
  FaCog,
} from 'react-icons/fa';

interface SelfVideoViewProps {
  isVideoOff: boolean;
  isMuted: boolean;
  onVideoToggle: () => void;
  currentQuestionId?: string;
  onVideoData?: (type: string, data: any) => void;
  isRecording?: boolean;
  isProcessing?: boolean;
  sessionId?: string;
}

interface VideoQuality {
  brightness: number;
  contrast: number;
  sharpness: number;
  overall: number;
}

interface VideoFrame {
  timestamp: number;
  data: string;
  quality: number;
  frameIndex: number;
}

export const SelfVideoView: React.FC<SelfVideoViewProps> = ({
  isVideoOff,
  isMuted,
  onVideoToggle,
  currentQuestionId,
  onVideoData,
  isRecording = false,
  isProcessing = false,
  sessionId,
}) => {
  const isMobile = useBreakpointValue({ base: true, md: false });
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const frameIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const recordingStartTimeRef = useRef<number>(0);
  const videoBufferRef = useRef<VideoFrame[]>([]);
  const chunkIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [hasCamera, setHasCamera] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [videoQuality, setVideoQuality] = useState<VideoQuality>({
    brightness: 0,
    contrast: 0,
    sharpness: 0,
    overall: 0,
  });
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [frameCount, setFrameCount] = useState(0);
  const [isInitializing, setIsInitializing] = useState(false);
  const [streamingStatus, setStreamingStatus] = useState<
    'idle' | 'streaming' | 'buffering' | 'error'
  >('idle');

  const initializeCamera = useCallback(async () => {
    if (isVideoOff) return;

    setIsInitializing(true);
    setCameraError(null);

    try {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
        streamRef.current = null;
      }

      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia is not supported in this browser');
      }

      const constraints = {
        video: {
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 },
          frameRate: { ideal: 30, min: 15 },
          facingMode: 'user',
          aspectRatio: { ideal: 16 / 9 },
          deviceId: undefined,
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: { ideal: 48000 },
          channelCount: { ideal: 1 },
        },
      };

      console.log('Camera constraints:', constraints);

      const mediaStream = await navigator.mediaDevices.getUserMedia(
        constraints
      );

      console.log('Media stream obtained:', {
        id: mediaStream.id,
        active: mediaStream.active,
        tracks: mediaStream.getTracks().map((track) => ({
          kind: track.kind,
          label: track.label,
          enabled: track.enabled,
          readyState: track.readyState,
        })),
      });

      streamRef.current = mediaStream;
      setHasCamera(true);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        try {
          await videoRef.current.play();
          console.log('Video element started playing');
        } catch (playError) {
          console.error('Video play failed:', playError);
        }
      }

      // ビデオ品質モニタリングを開始 - コメントアウト
      // startVideoQualityMonitoring();

      console.log('Camera initialized successfully');
    } catch (error: any) {
      console.error('Camera initialization failed:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        constraint: error.constraint,
      });
      setCameraError(getCameraErrorMessage(error));
      setHasCamera(false);
    } finally {
      setIsInitializing(false);
    }
  }, [isVideoOff]);

  // カメラエラーメッセージを取得
  const getCameraErrorMessage = (error: any): string => {
    if (error.name === 'NotAllowedError') {
      return 'カメラへのアクセスを許可してください';
    }
    if (error.name === 'NotFoundError') {
      return 'カメラが見つかりません';
    }
    if (error.name === 'NotReadableError') {
      return 'カメラは他のアプリケーションで使用中です';
    }
    if (error.name === 'OverconstrainedError') {
      return 'カメラは要求された品質をサポートしていません';
    }
    return 'カメラ初期化エラー';
  };

  // ビデオ品質のモニタリングとフレームのキャプチャ - 品質計算部分をコメントアウト
  const startVideoQualityMonitoring = useCallback(() => {
    if (!canvasRef.current || !videoRef.current) return;

    const monitorQuality = () => {
      if (!canvasRef.current || !videoRef.current || isVideoOff) return;

      const canvas = canvasRef.current;
      const video = videoRef.current;
      const ctx = canvas.getContext('2d');

      if (!ctx) return;

      // Set canvas size
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw video frame
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      /* コメントアウト - 品質分析
      // Analyze quality
      const quality = analyzeVideoQuality(ctx, canvas.width, canvas.height);
      setVideoQuality(quality);
      */

      // 品質分析をスキップ - デフォルト値を設定
      setVideoQuality({
        brightness: 80,
        contrast: 80,
        sharpness: 80,
        overall: 80
      });

      /* コメントアウト - 録画フロー
      // Capture frame nếu đang recording
      if (isRecording && onVideoData) {
        const frameData = canvas.toDataURL('image/jpeg', 0.8);
        const frame: VideoFrame = {
          timestamp: Date.now(),
          data: frameData.split(',')[1], // Remove data:image/jpeg;base64, prefix
          quality: quality.overall,
          frameIndex: frameCount,
        };

        // Add to buffer
        videoBufferRef.current.push(frame);
        setFrameCount((prev) => prev + 1);
      }
      */
    };

    // 100ミリ秒ごとにモニタリング (品質モニタリングのために10 FPS)
    frameIntervalRef.current = setInterval(monitorQuality, 100);
  }, [isVideoOff, isRecording, onVideoData, frameCount]);

  // Send video chunks to backend - TEMPORARILY COMMENTED OUT FOR SPEECH-TO-TEXT FOCUS
  const sendVideoChunks = useCallback(() => {
    if (!isRecording || !onVideoData || videoBufferRef.current.length === 0)
      return;

    // Clear buffer without sending to focus on speech-to-text
    videoBufferRef.current = [];

    /* COMMENTED OUT - VIDEO SENDING FLOW
    const chunks = [...videoBufferRef.current];
    videoBufferRef.current = []; // Clear buffer

    chunks.forEach((frame, index) => {
      onVideoData('video_chunk', {
        chunk_data: frame.data,
        timestamp: frame.timestamp,
        frame_index: frame.frameIndex,
        quality: frame.quality,
        question_id: currentQuestionId,
        session_id: sessionId,
      });
    });
    */

    setStreamingStatus('streaming');
  }, [isRecording, onVideoData, currentQuestionId, sessionId]);

  /* コメントアウト - ビデオ品質分析（明度、コントラスト、シャープネス計算）
  // Analyze video quality
  const analyzeVideoQuality = (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ): VideoQuality => {
    const imageData = ctx.getImageData(0, 0, width, height);
    const data = imageData.data;

    let totalBrightness = 0;
    let totalContrast = 0;
    let totalSharpness = 0;

    // 明度とコントラストを計算
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];

      const brightness = (r + g + b) / 3;
      totalBrightness += brightness;
    }

    const avgBrightness = totalBrightness / (data.length / 4);

    // コントラストを計算
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];

      const brightness = (r + g + b) / 3;
      totalContrast += Math.abs(brightness - avgBrightness);
    }

    const avgContrast = totalContrast / (data.length / 4);

    // シャープネスを計算 (エッジ検出)
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        const current = data[idx];
        const right = data[idx + 4];
        const bottom = data[idx + width * 4];

        const edgeX = Math.abs(current - right);
        const edgeY = Math.abs(current - bottom);
        totalSharpness += Math.sqrt(edgeX * edgeX + edgeY * edgeY);
      }
    }

    const avgSharpness = totalSharpness / ((width - 2) * (height - 2));

    // スコアを正規化 (0-100)
    const brightnessScore = Math.min(100, Math.max(0, avgBrightness / 2.55));
    const contrastScore = Math.min(100, Math.max(0, avgContrast / 2.55));
    const sharpnessScore = Math.min(100, Math.max(0, avgSharpness / 10));

    const overall = Math.round(
      (brightnessScore + contrastScore + sharpnessScore) / 3
    );

    return {
      brightness: Math.round(brightnessScore),
      contrast: Math.round(contrastScore),
      sharpness: Math.round(sharpnessScore),
      overall,
    };
  };
  */

  /* コメントアウト - 録画開始フロー
  // 録画開始
  const startRecording = useCallback(() => {
    if (!isRecording || !onVideoData) return;

    recordingStartTimeRef.current = Date.now();
    setRecordingDuration(0);
    setFrameCount(0);
    videoBufferRef.current = [];
    setStreamingStatus('streaming');

    // 録画開始シグナルをコメントアウト - 音声認識に集中
    // onVideoData('start_video_recording', {
    //   question_id: currentQuestionId,
    //   session_id: sessionId,
    //   timestamp: Date.now(),
    // });

    // 継続時間タイマーを開始
    const durationTimer = setInterval(() => {
      setRecordingDuration((prev) => prev + 1);
    }, 1000);

    // チャンク送信タイマーをコメントアウト - 音声認識に集中
    // chunkIntervalRef.current = setInterval(sendVideoChunks, 500);

    return () => {
      clearInterval(durationTimer);
      if (chunkIntervalRef.current) {
        clearInterval(chunkIntervalRef.current);
      }
    };
  }, [isRecording, onVideoData, currentQuestionId, sessionId, sendVideoChunks]);
  */

  /* コメントアウト - 録画停止フロー
  // 録画停止
  const stopRecording = useCallback(() => {
    if (!isRecording || !onVideoData) return;

    // 残りのチャンクを送信 (音声認識に集中するためコメントアウト)
    sendVideoChunks();

    // 録画終了シグナルをコメントアウト
    // onVideoData('end_video_recording', {
    //   question_id: currentQuestionId,
    //   session_id: sessionId,
    //   duration: recordingDuration,
    //   frame_count: frameCount,
    //   timestamp: Date.now(),
    // });

    setStreamingStatus('idle');
  }, [
    isRecording,
    onVideoData,
    currentQuestionId,
    sessionId,
    recordingDuration,
    frameCount,
    sendVideoChunks,
  ]);
  */

  // Effects
  useEffect(() => {
    initializeCamera();
  }, [initializeCamera]);

  /* コメントアウト - 録画エフェクト
  useEffect(() => {
    if (isRecording) {
      const cleanup = startRecording();
      return cleanup;
    } else {
      stopRecording();
    }
  }, [isRecording, startRecording, stopRecording]);
  */

  useEffect(() => {
    return () => {
      if (frameIntervalRef.current) {
        clearInterval(frameIntervalRef.current);
      }
      if (chunkIntervalRef.current) {
        clearInterval(chunkIntervalRef.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  // Format duration
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get quality color
  const getQualityColor = (quality: number): string => {
    if (quality >= 80) return 'green.400';
    if (quality >= 60) return 'yellow.400';
    if (quality >= 40) return 'orange.400';
    return 'red.400';
  };

  // Get streaming status color
  const getStreamingStatusColor = (): string => {
    switch (streamingStatus) {
      case 'streaming':
        return 'green.400';
      case 'buffering':
        return 'yellow.400';
      case 'error':
        return 'red.400';
      default:
        return 'gray.400';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, x: 100 }}
      animate={{ opacity: 1, scale: 1, x: 0 }}
      transition={{ delay: 0.6, duration: 0.5 }}
      style={{
        position: 'absolute',
        bottom: '140px',
        right: '32px',
        width: isMobile ? '160px' : '300px',
        height: isMobile ? '120px' : '225px',
        zIndex: 20,
      }}
    >
      <Box
        w="100%"
        h="100%"
        bg="rgba(15, 23, 42, 0.8)"
        backdropFilter="blur(20px)"
        borderRadius="3xl"
        overflow="hidden"
        border="3px solid"
        borderColor={
          isVideoOff || !hasCamera || isMuted
            ? 'red.400'
            : videoQuality.overall >= 80
            ? 'green.400'
            : videoQuality.overall >= 60
            ? 'yellow.400'
            : 'orange.400'
        }
        position="relative"
        cursor="default"
        /* カメラトグルフローを削除 */
        _hover={{ transform: 'scale(1.05)' }}
        transition="all 0.3s"
      >
        {/* Hidden canvas for quality analysis */}
        <canvas ref={canvasRef} style={{ display: 'none' }} />

        {!isVideoOff && hasCamera && streamRef.current ? (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: '24px',
              transform: 'scaleX(-1)',
            }}
          />
        ) : (
          <Flex
            w="100%"
            h="100%"
            align="center"
            justify="center"
            direction="column"
          >
            {isInitializing ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              >
                <FaCog
                  size={isMobile ? 32 : 52}
                  color="rgba(59, 130, 246, 0.8)"
                />
              </motion.div>
            ) : cameraError ? (
              <FaExclamationTriangle
                color="rgba(239, 68, 68, 0.8)"
                size={isMobile ? 32 : 52}
              />
            ) : (
              <FaVideoSlash
                color="rgba(255, 100, 100, 0.8)"
                size={isMobile ? 32 : 52}
              />
            )}
            <Text
              color="whiteAlpha.700"
              fontSize="sm"
              mt={2}
              textAlign="center"
            >
              {isInitializing
                ? 'カメラを初期化中...'
                : cameraError
                ? cameraError
                : !hasCamera
                ? 'カメラがありません'
                : 'カメラはオフです'}
            </Text>
          </Flex>
        )}

        {/* コメントアウト - 品質インジケーター
        {hasCamera && !isVideoOff && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            style={{
              position: 'absolute',
              top: '8px',
              left: '8px',
            }}
          >
            <Tooltip label={`品質: ${videoQuality.overall}%`}>
              <Badge
                colorScheme={
                  videoQuality.overall >= 80
                    ? 'green'
                    : videoQuality.overall >= 60
                    ? 'yellow'
                    : videoQuality.overall >= 40
                    ? 'orange'
                    : 'red'
                }
                variant="solid"
                fontSize="xs"
                borderRadius="full"
                px={2}
              >
                {videoQuality.overall}%
              </Badge>
            </Tooltip>
          </motion.div>
        )}
        */}

        {/* Recording Indicator */}
        {isRecording && (
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
            style={{
              position: 'absolute',
              top: '8px',
              right: '8px',
            }}
          >
            <Badge
              colorScheme="red"
              variant="solid"
              fontSize="xs"
              borderRadius="full"
              px={2}
            >
              REC {formatDuration(recordingDuration)}
            </Badge>
          </motion.div>
        )}

        {/* Streaming Status Indicator */}
        {isRecording && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            style={{
              position: 'absolute',
              bottom: '8px',
              right: '8px',
            }}
          >
            <Tooltip label={`ストリーミング状態: ${streamingStatus}`}>
              <motion.div
                style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: getStreamingStatusColor(),
                }}
                animate={
                  streamingStatus === 'streaming'
                    ? {
                        scale: [1, 1.2, 1],
                        opacity: [1, 0.7, 1],
                      }
                    : {}
                }
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              />
            </Tooltip>
          </motion.div>
        )}

        {isProcessing && (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            style={{
              position: 'absolute',
              bottom: '8px',
              left: '8px',
            }}
          >
            <Box
              w={6}
              h={6}
              borderRadius="full"
              border="2px solid"
              borderColor="blue.400"
              borderTopColor="transparent"
            />
          </motion.div>
        )}

        {isMuted && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            style={{
              position: 'absolute',
              bottom: '8px',
              left: isProcessing ? '40px' : '8px',
            }}
          >
            <Box bg="red.500" borderRadius="full" p={2}>
              <FaMicrophoneSlash size={12} color="white" />
            </Box>
          </motion.div>
        )}

        {isRecording && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            style={{
              position: 'absolute',
              bottom: '8px',
              left: '50%',
              transform: 'translateX(-50%)',
            }}
          >
            <Badge
              colorScheme="blue"
              variant="solid"
              fontSize="xs"
              borderRadius="full"
              px={2}
            >
              {frameCount} frames
            </Badge>
          </motion.div>
        )}
      </Box>
    </motion.div>
  );
};
