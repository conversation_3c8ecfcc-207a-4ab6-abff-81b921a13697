# Azure Speech Service Configuration
# Azure Portalから取得した認証情報を設定してください
# https://portal.azure.com/

# Speech Serviceのサブスクリプションキー
NEXT_PUBLIC_AZURE_SPEECH_KEY=your_azure_speech_key_here

# Speech Serviceのリージョン（例: japaneast, westus, eastus）
NEXT_PUBLIC_AZURE_SPEECH_REGION=your_azure_region_here

# API base URL for frontend to communicate with FastAPI backend
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080

# バックエンド用（フロントエンドと同じ値を設定）
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_region_here

# FastAPI backend settings
OPENAI_API_KEY=your_openai_api_key
GOOGLE_API_KEY=your_google_api_key

# Optional: Development Settings
# NODE_ENV=development
# DEBUG=true

# Optional: Database Configuration (将来の拡張用)
# DATABASE_URL=postgresql://user:password@localhost:5432/mensetsu_kun

# Optional: Session Secret (将来の認証実装用)
# SESSION_SECRET=your_session_secret_here

SECRET_KEY=mensetsu-kun-super-secret-key-2025
ALGORITHM=HS256

NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api
NEXT_PUBLIC_CANDIDATE_URL=http://localhost:3000
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_AGENT_URL=http://localhost:3001

NEXT_PUBLIC_DOMAIN=localhost
