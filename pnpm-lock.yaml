lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      framer-motion:
        specifier: ^12.16.0
        version: 12.18.1(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      microsoft-cognitiveservices-speech-sdk:
        specifier: ^1.44.0
        version: 1.44.1
      react-intersection-observer:
        specifier: ^9.16.0
        version: 9.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      zustand:
        specifier: ^5.0.5
        version: 5.0.5(@types/react@19.1.5)(immer@10.1.1)(react@18.3.1)(use-sync-external-store@1.5.0(react@18.3.1))
    devDependencies:
      concurrently:
        specifier: ^8.2.2
        version: 8.2.2
      kill-port:
        specifier: ^2.0.1
        version: 2.0.1
      tsx:
        specifier: ^4
        version: 4.20.3

  src/candidate:
    dependencies:
      '@azure/communication-calling':
        specifier: ^1.35.1
        version: 1.35.1
      '@azure/communication-common':
        specifier: ^2.3.1
        version: 2.4.0
      '@azure/communication-identity':
        specifier: ^1.3.1
        version: 1.3.1
      '@azure/communication-react':
        specifier: ^1.27.1
        version: 1.28.0(@azure/communication-calling-effects@1.1.4)(@azure/communication-calling@1.35.1)(@azure/communication-chat@1.5.4)(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@babylonjs/core':
        specifier: ^6.49.0
        version: 6.49.0
      '@babylonjs/gui':
        specifier: ^6.49.0
        version: 6.49.0(@babylonjs/core@6.49.0)
      '@chakra-ui/icons':
        specifier: ^2.2.4
        version: 2.2.4(@chakra-ui/react@2.10.9(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react':
        specifier: ^2.8.2
        version: 2.10.9(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@emotion/react':
        specifier: ^11.11.3
        version: 11.14.0(@types/react@19.1.5)(react@18.3.1)
      '@emotion/styled':
        specifier: ^11.11.0
        version: 11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-components':
        specifier: ^9.64.0
        version: 9.66.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@mensetsu-kun/shared':
        specifier: workspace:^1.0.0
        version: link:../shared
      '@types/axios':
        specifier: ^0.9.36
        version: 0.9.36
      axios:
        specifier: ^1.9.0
        version: 1.10.0
      dotenv:
        specifier: ^16.5.0
        version: 16.5.0
      framer-motion:
        specifier: ^11.18.2
        version: 11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      microsoft-cognitiveservices-speech-sdk:
        specifier: ^1.44.0
        version: 1.44.1
      next:
        specifier: 14.1.0
        version: 14.1.0(@babel/core@7.27.4)(@opentelemetry/api@1.9.0)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react:
        specifier: ^18.2.0
        version: 18.3.1
      react-dom:
        specifier: ^18.2.0
        version: 18.3.1(react@18.3.1)
      react-icons:
        specifier: ^5.5.0
        version: 5.5.0(react@18.3.1)
      typescript:
        specifier: ^5.3.3
        version: 5.8.3
    devDependencies:
      '@next/eslint-plugin-next':
        specifier: ^15.3.1
        version: 15.3.4
      '@types/node':
        specifier: 20.5.7
        version: 20.5.7
      '@types/react':
        specifier: ^19.0.1
        version: 19.1.5
      '@types/react-dom':
        specifier: ^19.1.2
        version: 19.1.6(@types/react@19.1.5)
      '@typescript-eslint/eslint-plugin':
        specifier: ^8.31.0
        version: 8.35.0(@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)
      '@typescript-eslint/parser':
        specifier: ^8.31.0
        version: 8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)
      autoprefixer:
        specifier: 10.4.19
        version: 10.4.19(postcss@8.4.38)
      eslint:
        specifier: ^9.25.1
        version: 9.29.0(jiti@1.21.7)
      eslint-config-prettier:
        specifier: ^10.1.2
        version: 10.1.5(eslint@9.29.0(jiti@1.21.7))
      eslint-plugin-import:
        specifier: ^2.31.0
        version: 2.31.0(@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.29.0(jiti@1.21.7))
      eslint-plugin-node:
        specifier: ^11.1.0
        version: 11.1.0(eslint@9.29.0(jiti@1.21.7))
      eslint-plugin-prettier:
        specifier: ^5.2.6
        version: 5.5.1(@types/eslint@9.6.1)(eslint-config-prettier@10.1.5(eslint@9.29.0(jiti@1.21.7)))(eslint@9.29.0(jiti@1.21.7))(prettier@3.6.1)
      eslint-plugin-react:
        specifier: ^7.37.5
        version: 7.37.5(eslint@9.29.0(jiti@1.21.7))
      eslint-plugin-react-hooks:
        specifier: ^5.2.0
        version: 5.2.0(eslint@9.29.0(jiti@1.21.7))
      eslint-plugin-unused-imports:
        specifier: ^4.1.4
        version: 4.1.4(@typescript-eslint/eslint-plugin@8.35.0(@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.29.0(jiti@1.21.7))
      postcss:
        specifier: 8.4.38
        version: 8.4.38
      tailwindcss:
        specifier: ^3.4.17
        version: 3.4.17

  src/management:
    dependencies:
      '@chakra-ui/icons':
        specifier: ^2.1.1
        version: 2.2.4(@chakra-ui/react@2.10.9(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react':
        specifier: ^2.8.2
        version: 2.10.9(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@emotion/react':
        specifier: ^11.11.3
        version: 11.14.0(@types/react@18.3.23)(react@18.3.1)
      '@emotion/styled':
        specifier: ^11.11.0
        version: 11.14.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1)
      '@mensetsu-kun/shared':
        specifier: workspace:^1.0.0
        version: link:../shared
      axios:
        specifier: ^1.6.7
        version: 1.10.0
      framer-motion:
        specifier: ^11.0.5
        version: 11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      next:
        specifier: ^14.1.0
        version: 14.1.0(@babel/core@7.27.4)(@opentelemetry/api@1.9.0)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react:
        specifier: ^18.2.0
        version: 18.3.1
      react-dom:
        specifier: ^18.2.0
        version: 18.3.1(react@18.3.1)
      react-icons:
        specifier: ^5.5.0
        version: 5.5.0(react@18.3.1)
    devDependencies:
      '@types/react':
        specifier: ^18.2.0
        version: 18.3.23
      babel-loader:
        specifier: ^10.0.0
        version: 10.0.0(@babel/core@7.27.4)(webpack@5.99.9)
      ts-loader:
        specifier: ^9.5.2
        version: 9.5.2(typescript@5.8.3)(webpack@5.99.9)
      typescript:
        specifier: 5.8.3
        version: 5.8.3

  src/shared: {}

packages:

  '@alloc/quick-lru@5.2.0':
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@azure-rest/core-client@2.4.0':
    resolution: {integrity: sha512-CjMFBcmnt0YNdRcxSSoZbtZNXudLlicdml7UrPsV03nHiWB+Bq5cu5ctieyaCuRtU7jm7+SOFtiE/g4pBFPKKA==}
    engines: {node: '>=18.0.0'}

  '@azure/abort-controller@1.1.0':
    resolution: {integrity: sha512-TrRLIoSQVzfAJX9H1JeFjzAoDGcoK1IYX1UImfceTZpsyYfWr09Ss1aHW1y5TrrR3iq6RZLBwJ3E24uwPhwahw==}
    engines: {node: '>=12.0.0'}

  '@azure/abort-controller@2.1.2':
    resolution: {integrity: sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA==}
    engines: {node: '>=18.0.0'}

  '@azure/communication-calling-effects@1.1.4':
    resolution: {integrity: sha512-0lMBeJ+H7bYwjwdCln6qycUEi7/JkT9X3QF/BoLkeiMQ6Uk9HWRuO9kzTA33JHSfOuAA7Ybt7cjdQcz0bAqV2g==}

  '@azure/communication-calling@1.35.1':
    resolution: {integrity: sha512-b6K+OkifuwkGrUMFngBazHfGL8dRjwYi+UWRe3Q8inBHeWNSLozHXZLgzyDpZN0BVnHeO7Av0L5nQor4taFNFw==}

  '@azure/communication-chat@1.5.4':
    resolution: {integrity: sha512-sC5IACFEGgvGbt6qzL3kocdMbhuWkldkE0HHsUfRPokPKiK4IfVCpn2syJcc9R8ts6hk0N5XvEY04KM2yayhJg==}
    engines: {node: '>=18.0.0'}

  '@azure/communication-common@2.4.0':
    resolution: {integrity: sha512-wwn4AoOgTgoA9OZkO34SKBpQg7/kfcABnzbaYEbc+9bCkBtwwjgMEk6xM+XLEE/uuODZ8q8jidUoNcZHQyP5AQ==}
    engines: {node: '>=18.0.0'}

  '@azure/communication-identity@1.3.1':
    resolution: {integrity: sha512-S54UTeEM3SbUNGFeGcGQEw64KLUu8CmZi2/2hRu3sy6Rx2i4Y8aL+ITlGC7bOm2v5rKELD5PoXAVYDJxVCJN1w==}
    engines: {node: '>=18.0.0'}

  '@azure/communication-react@1.28.0':
    resolution: {integrity: sha512-Q+U1rGmTaZO+F9cHH/7NmQA2Y/vlZRImNWU9LCOlkj3UHogG+rocXjzsp5cUVCxXFDQzeW45D16MfPPBRaAq1g==}
    peerDependencies:
      '@azure/communication-calling': ^1.35.1
      '@azure/communication-calling-effects': ^1.1.4
      '@azure/communication-chat': '>=1.5.4'
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@azure/communication-signaling@1.0.0-beta.29':
    resolution: {integrity: sha512-bTNxJaS000wjtbLlH/nju3Off9uSybYeRVLvx1Y5k3ajb1rqVGUcsIduSEz1JBf+dRQx9pHRDCAEAXs/hOvzpg==}
    engines: {node: '>=8.0.0'}

  '@azure/core-auth@1.9.0':
    resolution: {integrity: sha512-FPwHpZywuyasDSLMqJ6fhbOK3TqUdviZNF8OqRGA4W5Ewib2lEEZ+pBsYcBa88B2NGO/SEnYPGhyBqNlE8ilSw==}
    engines: {node: '>=18.0.0'}

  '@azure/core-client@1.9.4':
    resolution: {integrity: sha512-f7IxTD15Qdux30s2qFARH+JxgwxWLG2Rlr4oSkPGuLWm+1p5y1+C04XGLA0vmX6EtqfutmjvpNmAfgwVIS5hpw==}
    engines: {node: '>=18.0.0'}

  '@azure/core-lro@2.7.2':
    resolution: {integrity: sha512-0YIpccoX8m/k00O7mDDMdJpbr6mf1yWo2dfmxt5A8XVZVVMz2SSKaEbMCeJRvgQ0IaSlqhjT47p4hVIRRy90xw==}
    engines: {node: '>=18.0.0'}

  '@azure/core-paging@1.6.2':
    resolution: {integrity: sha512-YKWi9YuCU04B55h25cnOYZHxXYtEvQEbKST5vqRga7hWY9ydd3FZHdeQF8pyh+acWZvppw13M/LMGx0LABUVMA==}
    engines: {node: '>=18.0.0'}

  '@azure/core-rest-pipeline@1.21.0':
    resolution: {integrity: sha512-a4MBwe/5WKbq9MIxikzgxLBbruC5qlkFYlBdI7Ev50Y7ib5Vo/Jvt5jnJo7NaWeJ908LCHL0S1Us4UMf1VoTfg==}
    engines: {node: '>=18.0.0'}

  '@azure/core-tracing@1.2.0':
    resolution: {integrity: sha512-UKTiEJPkWcESPYJz3X5uKRYyOcJD+4nYph+KpfdPRnQJVrZfk0KJgdnaAWKfhsBBtAf/D58Az4AvCJEmWgIBAg==}
    engines: {node: '>=18.0.0'}

  '@azure/core-util@1.12.0':
    resolution: {integrity: sha512-13IyjTQgABPARvG90+N2dXpC+hwp466XCdQXPCRlbWHgd3SJd5Q1VvaBGv6k1BIa4MQm6hAF1UBU1m8QUxV8sQ==}
    engines: {node: '>=18.0.0'}

  '@azure/logger@1.2.0':
    resolution: {integrity: sha512-0hKEzLhpw+ZTAfNJyRrn6s+V0nDWzXk9OjBr2TiGIu0OfMr5s2V4FpKLTAK3Ca5r5OKLbf4hkOGDPyiRjie/jA==}
    engines: {node: '>=18.0.0'}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.27.5':
    resolution: {integrity: sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.27.4':
    resolution: {integrity: sha512-bXYxrXFubeYdvB0NhD/NBB3Qi6aZeV20GOWVI47t2dkecCEoneR4NPVcb7abpXDEvejgrUfFtG6vG/zxAKmg+g==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.5':
    resolution: {integrity: sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.3':
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.6':
    resolution: {integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.5':
    resolution: {integrity: sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/runtime@7.27.6':
    resolution: {integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.2':
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.4':
    resolution: {integrity: sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.6':
    resolution: {integrity: sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==}
    engines: {node: '>=6.9.0'}

  '@babylonjs/core@6.49.0':
    resolution: {integrity: sha512-jU/JyqebRqqziNwHLcBYzANrVRd9S55yNZEjejwg2p4I8NRnoBBNgf4wuUVw17UKNHc1v3KD/Vnr5C2+dIWAqQ==}

  '@babylonjs/gui@6.49.0':
    resolution: {integrity: sha512-ZrfhWpvsva35P4NW/Ox8RiSJJ2RfTwU+azRZQDatWXHg86p8Lxu7oDEOg4ALiMLHn3cYkfDMXv29euLCLKEfxw==}
    peerDependencies:
      '@babylonjs/core': ^6.0.0

  '@chakra-ui/anatomy@2.3.6':
    resolution: {integrity: sha512-TjmjyQouIZzha/l8JxdBZN1pKZTj7sLpJ0YkFnQFyqHcbfWggW9jKWzY1E0VBnhtFz/xF3KC6UAVuZVSJx+y0g==}

  '@chakra-ui/hooks@2.4.5':
    resolution: {integrity: sha512-601fWfHE2i7UjaxK/9lDLlOni6vk/I+04YDbM0BrelJy+eqxdlOmoN8Z6MZ3PzFh7ofERUASor+vL+/HaCaZ7w==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/icons@2.2.4':
    resolution: {integrity: sha512-l5QdBgwrAg3Sc2BRqtNkJpfuLw/pWRDwwT58J6c4PqQT6wzXxyNa8Q0PForu1ltB5qEiFb1kxr/F/HO1EwNa6g==}
    peerDependencies:
      '@chakra-ui/react': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/react@2.10.9':
    resolution: {integrity: sha512-lhdcgoocOiURwBNR3L8OioCNIaGCZqRfuKioLyaQLjOanl4jr0PQclsGb+w0cmito252vEWpsz2xRqF7y+Flrw==}
    peerDependencies:
      '@emotion/react': '>=11'
      '@emotion/styled': '>=11'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'

  '@chakra-ui/styled-system@2.12.4':
    resolution: {integrity: sha512-oa07UG7Lic5hHSQtGRiMEnYjuhIa8lszyuVhZjZqR2Ap3VMF688y1MVPJ1pK+8OwY5uhXBgVd5c0+rI8aBZlwg==}

  '@chakra-ui/theme-tools@2.2.9':
    resolution: {integrity: sha512-PcbYL19lrVvEc7Oydy//jsy/MO/rZz1DvLyO6AoI+bI/+Kwz9WfOKsspbulEhRg5COayE0R/IZPsskXZ7Mp4bA==}
    peerDependencies:
      '@chakra-ui/styled-system': '>=2.0.0'

  '@chakra-ui/theme@3.4.9':
    resolution: {integrity: sha512-GAom2SjSdRWTcX76/2yJOFJsOWHQeBgaynCUNBsHq62OafzvELrsSHDUw0bBqBb1c2ww0CclIvGilPup8kXBFA==}
    peerDependencies:
      '@chakra-ui/styled-system': '>=2.8.0'

  '@chakra-ui/utils@2.2.5':
    resolution: {integrity: sha512-KTBCK+M5KtXH6p54XS39ImQUMVtAx65BoZDoEms3LuObyTo1+civ1sMm4h3nRT320U6H5H7D35WnABVQjqU/4g==}
    peerDependencies:
      react: '>=16.8.0'

  '@ctrl/tinycolor@3.6.1':
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}

  '@emotion/babel-plugin@11.13.5':
    resolution: {integrity: sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==}

  '@emotion/cache@11.14.0':
    resolution: {integrity: sha512-L/B1lc/TViYk4DcpGxtAVbx0ZyiKM5ktoIyafGkH6zg/tj+mA+NE//aPYKG0k8kCHSHVJrpLpcAlOBEXQ3SavA==}

  '@emotion/hash@0.9.2':
    resolution: {integrity: sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==}

  '@emotion/is-prop-valid@1.3.1':
    resolution: {integrity: sha512-/ACwoqx7XQi9knQs/G0qKvv5teDMhD7bXYns9N/wM8ah8iNb8jZ2uNO0YOgiq2o2poIvVtJS2YALasQuMSQ7Kw==}

  '@emotion/memoize@0.9.0':
    resolution: {integrity: sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==}

  '@emotion/react@11.14.0':
    resolution: {integrity: sha512-O000MLDBDdk/EohJPFUqvnp4qnHeYkVP5B0xEG0D/L7cOKP9kefu2DXn8dj74cQfsEzUqh+sr1RzFqiL1o+PpA==}
    peerDependencies:
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@emotion/serialize@1.3.3':
    resolution: {integrity: sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==}

  '@emotion/sheet@1.4.0':
    resolution: {integrity: sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==}

  '@emotion/styled@11.14.0':
    resolution: {integrity: sha512-XxfOnXFffatap2IyCeJyNov3kiDQWoR08gPUQxvbL7fxKryGBKUZUkG6Hz48DZwVrJSVh9sJboyV1Ds4OW6SgA==}
    peerDependencies:
      '@emotion/react': ^11.0.0-rc.0
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@emotion/unitless@0.10.0':
    resolution: {integrity: sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg==}

  '@emotion/use-insertion-effect-with-fallbacks@1.2.0':
    resolution: {integrity: sha512-yJMtVdH59sxi/aVJBpk9FQq+OR8ll5GT8oWd57UpeaKEVGab41JWaCFA7FRLoMLloOZF/c/wsPoe+bfGmRKgDg==}
    peerDependencies:
      react: '>=16.8.0'

  '@emotion/utils@1.4.2':
    resolution: {integrity: sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA==}

  '@emotion/weak-memoize@0.4.0':
    resolution: {integrity: sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==}

  '@esbuild/aix-ppc64@0.25.5':
    resolution: {integrity: sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.5':
    resolution: {integrity: sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.5':
    resolution: {integrity: sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.5':
    resolution: {integrity: sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.5':
    resolution: {integrity: sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.5':
    resolution: {integrity: sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.5':
    resolution: {integrity: sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.5':
    resolution: {integrity: sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.5':
    resolution: {integrity: sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.5':
    resolution: {integrity: sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.5':
    resolution: {integrity: sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.5':
    resolution: {integrity: sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.5':
    resolution: {integrity: sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.5':
    resolution: {integrity: sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.5':
    resolution: {integrity: sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.5':
    resolution: {integrity: sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.5':
    resolution: {integrity: sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.5':
    resolution: {integrity: sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.5':
    resolution: {integrity: sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.5':
    resolution: {integrity: sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.5':
    resolution: {integrity: sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.25.5':
    resolution: {integrity: sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.5':
    resolution: {integrity: sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.5':
    resolution: {integrity: sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.5':
    resolution: {integrity: sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.20.1':
    resolution: {integrity: sha512-OL0RJzC/CBzli0DrrR31qzj6d6i6Mm3HByuhflhl4LOBiWxN+3i6/t/ZQQNii4tjksXi8r2CRW1wMpWA2ULUEw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/config-helpers@0.2.3':
    resolution: {integrity: sha512-u180qk2Um1le4yf0ruXH3PYFeEZeYC3p/4wCTKrr2U1CmGdzGi3KtY0nuPDH48UJxlKCC5RDzbcbh4X0XlqgHg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.14.0':
    resolution: {integrity: sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.15.1':
    resolution: {integrity: sha512-bkOp+iumZCCbt1K1CmWf0R9pM5yKpDv+ZXtvSyQpudrI9kuFLp+bM2WOPXImuD/ceQuaa8f5pj93Y7zyECIGNA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.3.1':
    resolution: {integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.29.0':
    resolution: {integrity: sha512-3PIF4cBw/y+1u2EazflInpV+lYsSG0aByVIQzAgb1m1MhHFSbqTyNqtBKHgWf/9Ykud+DhILS9EGkmekVhbKoQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.6':
    resolution: {integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.3.3':
    resolution: {integrity: sha512-1+WqvgNMhmlAambTvT3KPtCl/Ibr68VldY2XY40SL1CE0ZXiakFR/cbTspaF5HsnpDMvcYYoJHfl4980NBjGag==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@floating-ui/core@1.7.1':
    resolution: {integrity: sha512-azI0DrjMMfIug/ExbBaeDVJXcY0a7EPvPjb2xAJPa4HeimBX+Z18HK8QQR3jb6356SnDDdxx+hinMLcJEDdOjw==}

  '@floating-ui/devtools@0.2.1':
    resolution: {integrity: sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==}
    peerDependencies:
      '@floating-ui/dom': '>=1.5.4'

  '@floating-ui/dom@1.7.1':
    resolution: {integrity: sha512-cwsmW/zyw5ltYTUeeYJ60CnQuPqmGwuGVhG9w0PRaRKkAyi38BT5CKrpIbb+jtahSwUl04cWzSx9ZOIxeS6RsQ==}

  '@floating-ui/utils@0.2.9':
    resolution: {integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==}

  '@fluentui-contrib/react-chat@0.1.11':
    resolution: {integrity: sha512-44d3JnNZ+hhxLdHey9/kwfAvkskl2TAcQU7gWtOykV6q/YXbGMRLELgE0FaA7WGOiJA70qms0zbgtdwSZBiXDA==}
    peerDependencies:
      '@fluentui/react-components': '>=9.25.1 <10.0.0'
      '@fluentui/react-icons': '>=2.0.204 <3.0.0'
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/date-time-utilities@8.6.10':
    resolution: {integrity: sha512-Bxq8DIMkFvkpCA1HKtCHdnFwPAnXLz3TkGp9kpi2T6VIv6VtLVSxRn95mbsUydpP9Up/DLglp/z9re5YFBGNbw==}

  '@fluentui/dom-utilities@2.3.10':
    resolution: {integrity: sha512-6WDImiLqTOpkEtfUKSStcTDpzmJfL6ZammomcjawN9xH/8u8G3Hx72CIt2MNck9giw/oUlNLJFdWRAjeP3rmPQ==}

  '@fluentui/font-icons-mdl2@8.5.62':
    resolution: {integrity: sha512-8yIJ1RlOJIXNS+Ac3dKL8LucFlnAPK3jbzYWRNq7rg1pVSdbtCmFCz1eEAgLxVdAfjx1/9Bei/yQr5Fv7WwV5Q==}

  '@fluentui/foundation-legacy@8.4.28':
    resolution: {integrity: sha512-u4ej3J7zl/DG9gYK48uz3QCVu6wLxhDYDCt4YDtz0kjtgWLeK3To3/nn6aRoauMytGzoJpAsJJVy6OQiGGIMBA==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'

  '@fluentui/keyboard-key@0.4.23':
    resolution: {integrity: sha512-9GXeyUqNJUdg5JiQUZeGPiKnRzMRi9YEUn1l9zq6X/imYdMhxHrxpVZS12129cBfgvPyxt9ceJpywSfmLWqlKA==}

  '@fluentui/keyboard-keys@9.0.8':
    resolution: {integrity: sha512-iUSJUUHAyTosnXK8O2Ilbfxma+ZyZPMua5vB028Ys96z80v+LFwntoehlFsdH3rMuPsA8GaC1RE7LMezwPBPdw==}

  '@fluentui/merge-styles@8.6.14':
    resolution: {integrity: sha512-vghuHFAfQgS9WLIIs4kgDOCh/DHd5vGIddP4/bzposhlAVLZR6wUBqldm9AuCdY88r5LyCRMavVJLV+Up3xdvA==}

  '@fluentui/priority-overflow@9.1.15':
    resolution: {integrity: sha512-/3jPBBq64hRdA416grVj+ZeMBUIaKZk2S5HiRg7CKCAV1JuyF84Do0rQI6ns8Vb9XOGuc4kurMcL/UEftoEVrg==}

  '@fluentui/react-accordion@9.7.0':
    resolution: {integrity: sha512-DzWK3RBWlREn9EUYEXdYZhC6cjJLAm2u21qqofovrIlU/LDUUCC1cPxJHycdi9KwP7mDZdhXSqQG6LLe9xIeMQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-alert@9.0.0-beta.124':
    resolution: {integrity: sha512-yFBo3B5H9hnoaXxlkuz8wRz04DEyQ+ElYA/p5p+Vojf19Zuta8DmFZZ6JtWdtxcdnnQ4LvAfC5OYYlzdReozPA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-aria@9.15.0':
    resolution: {integrity: sha512-8cN9/5+XHL3mzp1gNIj0ZXuPTioYALO/1FCWugkOF5JP8PVkV3HDX3ezRq2Bk44PS2YK98tjffTiBzFeanHxug==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-avatar@9.8.1':
    resolution: {integrity: sha512-hLOFxN8oqRkO8lBqGhXLONtI4LRWf/16TJDiizWbfep33NMS/rpHl+PijwO873CXRxSDnR1z3sENHpVInILtug==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-badge@9.3.0':
    resolution: {integrity: sha512-BFONtrI0SZmM+j+wR8tb5S43qodY5AydKMCJ35e02rR1/nyizg4tA3g/3iujGHAAsXPX04D20W4QMcy9LyRAXA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-breadcrumb@9.2.1':
    resolution: {integrity: sha512-xwrwLz8AbvfcbESviNOrQD4GZ8YeabDK/WLzVXPf+sWsnPnnYx+j/+EgnsbTjJ8FtYKkak1pMq6KwLC1mzWQnQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-button@9.5.0':
    resolution: {integrity: sha512-J4Tdxcey6cjyxKuRAQkUynAwBwLnuTmGry9APGddbnGPGXBDNqjHIqqMDua5lOSIINSIiQHTNdg7fZWoETSZ4Q==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-card@9.3.0':
    resolution: {integrity: sha512-ZvCuFta3X2HaLTU0fdpbHCz/j+jGYRhwC0CVcxK1u4cXb74r4V2DfXaNYI9vXw9ELGe3YoiOE7xmDdDL0sRUYQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-carousel@9.7.1':
    resolution: {integrity: sha512-nmr1QCzH5vZHZ6KQ50YK+1obfKr/hejgqSMu1Ze/CwZ2/louEYzN2bhibtJfW6b3PpBeowL+S26jbdNWtI78yg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-checkbox@9.4.1':
    resolution: {integrity: sha512-lrf4I12fGMrodQODjrwTgDl5bOssXuEzg+ioMh/ldWQGD6xPjoqrznLusfjj+Ua1qR6k2bHnHuSDoH7E1vzlng==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-color-picker@9.1.0':
    resolution: {integrity: sha512-Tm85dMk0XPUZDCybjd0sa+1txR38ejLL+MG/Z03cpC41GxihDh5+4dPAqSfPzfezbENNoFsqfjKiKhw0Un96Rg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-combobox@9.15.1':
    resolution: {integrity: sha512-/WmfxkrYwe3/XU4gan56tjEBVdBmG43tW247vqXHQiC/e3q/dsqwQNhCO/VVr2pTS/Y3xhorMML63Azh9WXJ4A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-components@9.62.0':
    resolution: {integrity: sha512-NfG0GlmXgDff0lFzOsthkfxKB2gdDk/YSlxf+h9n3PvQHkYL3pqTAjao4fiNRb1QlvNssMNPHVOBPD7/l7vSBQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-components@9.66.1':
    resolution: {integrity: sha512-Rzh+QL2reQEMaFLu+h314ic7w8W9TbDcyDpohb+CRODgT3YCw+Gt+SVbR3Yi+8Cf3kwtokDQIC3ki6iBQ9g/Tg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-context-selector@9.2.0':
    resolution: {integrity: sha512-s35dNhIcHGm6SmmQr04vATaogQZ2Wvl1zi4/xgZ4/6V8XAGPBqRRTkutjkWgW4u4WZDriWdWNL62ju3hGDpE9g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'
      scheduler: '>=0.19.0 <=0.23.0'

  '@fluentui/react-dialog@9.13.1':
    resolution: {integrity: sha512-YCGTh4IPaHQH1LTLoD5D5Ql7DK+1ytMHYL4kQ9O8CmSu3WntjUSmOKGxWDHqHLEX0gRz86fPy49/u5NDDhLfFA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-divider@9.3.0':
    resolution: {integrity: sha512-8MvWlNcYQBIpIH8d90PRLYvqTA53t0Folv1xf2isC+YWeTm5J1siZtPRiZ9+K0uqI9Y+RD4fnWN8HfMeyOAjlw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-drawer@9.8.1':
    resolution: {integrity: sha512-VjzG0qAXN7eXiBbFzM7YHpNes05YIdY3WHJD6V2FheHvmthzhw8GFqDnRHsZ581Wb9uB9xqi+WJ69vNJ9tS48Q==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-field@9.3.1':
    resolution: {integrity: sha512-9bzicAbR5+AtboowO6akbJsoMWDGUtbGenQT81mXt7HGg6RP86gpodgcr/4f1OG1w5VtrfoA/aoNExP/XzUeGg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-file-type-icons@8.13.1':
    resolution: {integrity: sha512-Be4Y4VkSFqNrKgGB1OswuRUgfEX2F9GV3UwV7vM/Zga53lX5ikzBH7aYjmAI4NdBQPYnXpt8ak+cH0OYBB8kdQ==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'

  '@fluentui/react-focus@8.9.25':
    resolution: {integrity: sha512-okDwer2ZHSBVEm6ISY2moruCuxa4Y0+AqP7DdoZpceoaAdHURZyLP3j7wxCEJEMRSoqKu5C6xNGn/Rxd5xPXLw==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'

  '@fluentui/react-hooks@8.8.19':
    resolution: {integrity: sha512-uXcETVTl2L0G/Ocyb2Rjym96tcJd2NaZ2Hqt6EJcBb9KJD9irNeXjCCxsRNPC5kBDbfrQML2aai+M2kU9lZKNQ==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'

  '@fluentui/react-icons@2.0.303':
    resolution: {integrity: sha512-XsPCS6ZWpXytoYSiRAZY1De82JXdkEhmn82cNWsnxlixQATCOv5+JTW1Isogp84eISYVZE3KkXPwqye8fpKnjg==}
    peerDependencies:
      react: '>=16.8.0 <19.0.0'

  '@fluentui/react-image@9.2.0':
    resolution: {integrity: sha512-vP26rQDNx5LevbEKbf6dLjTx4uOZWIopjx6HQYSLk8axGWmjXe21t6BXRa9iTiPfibwJmWwzXvqGHxYR/as/wA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-infobutton@9.0.0-beta.102':
    resolution: {integrity: sha512-3kA4F0Vga8Ds6JGlBajLCCDOo/LmPuS786Wg7ui4ZTDYVIMzy1yp2XuVcZniifBFvEp0HQCUoDPWUV0VI3FfzQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-infolabel@9.3.1':
    resolution: {integrity: sha512-fL2J3PJy6ylPQrFFwAJgFoACxRk5d/PtzjL7JlmM1OCaUmUD2FuUovDYpubw9r36OemVOHTB/oXhpitlS3BoPQ==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-input@9.6.1':
    resolution: {integrity: sha512-IMwJxKjZYznlKFrZ6MoKpFXJxfGoJBJux4hDZzqDWyDafDSvjmTpiiutJbQmMRQpxQ4pPuaHBwcSScfExAf69g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-jsx-runtime@9.1.0':
    resolution: {integrity: sha512-HB4+1ofzmweSWrFPZeoeepzNNHu54jplCfPLlppBoHx1MZ11RR9w2uIsLjfSDrEPIZnXbQxVBItvDh9ZrU9new==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-label@9.2.0':
    resolution: {integrity: sha512-WDaBR9AmdPvJ0vXN9WicOlHFhI6BKgQXULl0YjMXuL51tl37txyvY2crv+YNeVsfykI18h6LOPxltPeEdAsxag==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-link@9.5.0':
    resolution: {integrity: sha512-bdEFARlbnTyzrKHKv7wvLMRua7/gUX1dOzBG+1tfmJFuFkE2gz7rxABBVdlaI1PHsgAbGnzQnSzl6C5DOPgqcA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-list@9.2.1':
    resolution: {integrity: sha512-UGRD+oBNtSRA+GH7n3qC07AatNvRLBQwSCoaza9ElYWsh4eWQzbp/zkurLWIM0PrAUd4JHuMswHARRBlJeY5gg==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-menu@9.17.1':
    resolution: {integrity: sha512-aygFQRa6Zt8sZ6aBnR+OiNaFOmykg+X5BTPBiu2m6IlJs1Z42S2AuSj8OuBUjrFQ3LnxT579AHDZuTXBngCsEQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-message-bar@9.5.0':
    resolution: {integrity: sha512-rsJUrXQWazdQ8gUX+l4XzToA8BMOJ+8t6WjXYr48Ztp7E9oROKaralavF78yihwY3t1ceacSbKa4bQLNqONlDw==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-motion-components-preview@0.6.0':
    resolution: {integrity: sha512-9PBaI25VGIuVKYE8Q4gew4/tsFmsOD4F1ZzHdEVkUS984pCZjC3LD5+6wrxpoJajDGk4cpWRRGl8x3DcO5CgHQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-motion@9.8.0':
    resolution: {integrity: sha512-TTwJV4iw7LHesPNtQpPmEb77YplC89Vh2+ru2vWS+f5YJbmduN4V/WH/ViakHjRGj/m03jRaQruTpg3rKGUCZw==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-nav@9.1.1':
    resolution: {integrity: sha512-kn+5KVDCoY/xPrpEegJv9SEVofqLOPLDWk2C5YBR0zZItzZ7cHfNxABsZ3fD0RM15ro5BaaHm6mfuDxERHluHA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-overflow@9.4.1':
    resolution: {integrity: sha512-qToEgEuyBWN2Te+9gg56fib/jCDwi3gBJhvZQSL8Ywgg3nNhmyAnOfGEdaMHrVL4DpFaNEOzxoC2C9vrzCx5bQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-persona@9.4.1':
    resolution: {integrity: sha512-+1LLEfSEsZqcYLKt80BPT7hPXwbP49SiOb5PSHvOM58HtruWtD+rx7xLFVcR9BnlJK/oZkRjisfQlAM3zuZ3Yw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-popover@9.11.1':
    resolution: {integrity: sha512-f+/K+8zHAlrUR16NSEtZ4rYArPtm+PpEuC9qd7+PjrlI/GytZHqVlNA8X4ddHWQy+fJoqTSA6cbB+SEYK8/yPw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-portal-compat-context@9.0.13':
    resolution: {integrity: sha512-N+c6Qs775jnr/4WIzsQuNaRu4v16fa+gGsOCzzU1bqxX0IR9BSjjO2oLGC6luaAOqlQP+JIwn/aumOIJICKXkA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-portal@9.6.0':
    resolution: {integrity: sha512-FiA3eM/1Um/3HZvfaGisdL7pLV4idWzlmDUIFBUOlzXsy57mIY9IwV5nDHYiJdEMkW0UstRVJB4oRaHoHGSqUg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-positioning@9.18.1':
    resolution: {integrity: sha512-+ueJus7IaezMAEDrlo3G/ihd+8Voa1W4dWrswH7Jknulggp8Mfaz1wMdZq8GvMuBnifMLJ33M9svsrJJahscPw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-progress@9.3.1':
    resolution: {integrity: sha512-2+jMPtuANnU7mUVEyUhhLh2LJmZNHrH4sin5rjSlsipr3ifhCoFUOoOloHw+cuVFzHeQNxIV9AuzOODii6cU3g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-provider@9.21.0':
    resolution: {integrity: sha512-mADFjeZKN5e6AJJ45Nc99yDMmvzDPZea7G0PznByC4H/+JuZO3oExTve2SYSmj4KECyjv3wQVlMe7os9sCLZ6w==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-radio@9.4.1':
    resolution: {integrity: sha512-uQ+BeJeESBpC+MOC1coeiUlLVshpz2fjme3SKPuGDZv1x919Mh2e8OG5R1EcNGLJBMSVrU/LT8sqAV9WJ4k2cQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-rating@9.2.0':
    resolution: {integrity: sha512-GjEE6XmxDc8zTiQWZmiRJgXqKzreREQRUOimuBrG4exxKcoXj11Ah+oOrLJ/z/KmPyu0JGk5yHJ+VMuJeJh6gw==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-search@9.2.1':
    resolution: {integrity: sha512-tFfo72YnBLK4nIIpaL8IE0Qu1hHGOjbbl2TxM6NN9qddp0s+5WeUHtpE1auyMeY4s1UQNbZbtjmsBpzicCAlaQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-select@9.3.1':
    resolution: {integrity: sha512-BvylsBcUzH8t/miTo/kesuv6GgTW6AiipFkTFsoeKqXS4kWYOZx3+ufVytdU9Pcowr0WrSBy6s/206JCQR3nVg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-shared-contexts@9.23.1':
    resolution: {integrity: sha512-mP+7talxLz7n0G36o7Asdvst+JPzUbqbnoMKUWRVB5YwzlOXumEgaQDgL1BkRUJYaDGOjIiSTUjHOEkBt7iSdg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-skeleton@9.3.1':
    resolution: {integrity: sha512-gI05SgPkrACHH7dy2ZM5had1/Px99Wpvsxl+gzBCzloqeNlm0Eh1H/TH5UdFOm+0IA/Lit/8crwqSNRmHL/Viw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-slider@9.4.1':
    resolution: {integrity: sha512-pJeh2gRXV4/uDbT2HAcWmp7zxq3Bwr48/LHzsPngwKP6W8Pgw7NysMZimJVs3B5nL4KXZyyH/ArDy6IV7pl/Aw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-spinbutton@9.4.1':
    resolution: {integrity: sha512-dUj4XEocE5Uy0TWFxFNVGyRZpNJCHNl/VNWwJcDPNf6Jb5ThqGcXZ4IgWO00GoucwTkUzIHE37SSBGatL3ANsA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-spinner@9.6.0':
    resolution: {integrity: sha512-yRUozOphh92DMM/hZLp2aF12vWGpz70M7ya//E0PVhwXMD2zJf7EvK/HvgdtMNoiSkM9nYrEoe4HuEialn2WQQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-swatch-picker@9.3.1':
    resolution: {integrity: sha512-W7Dz9pF39KdNdYLFR6ySa13et/i+5LLkY6HrGg9k3LxtAYwCeooy++4FBYpWE87i+FcuiAGKmzhy6vHM5i2TBA==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-switch@9.3.1':
    resolution: {integrity: sha512-QxmTGQQdUWpfGe40RafooeHeM8evAz6dItDsEEenu4h8KbrD0fztBjDG51fjuAPsrbYzoPS1o684+dD8pl2tNw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-table@9.17.1':
    resolution: {integrity: sha512-iDaX/wK4UmxYoqUPNK84553UTiYBB3YwPPjIkpxoxlv+RnjnPDshmDRT4KzCDNI2NvuhinwaKtj+b8DvMnFwHA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tabs@9.8.0':
    resolution: {integrity: sha512-0dwF8v2rSRd7c3XV+LiHlf4eetXf79S2iBmLUZKmi+BQHWZv9NhmDLOw6DE8yidcHvlKlvXcUz+UNmVLXdmsCw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tabster@9.25.0':
    resolution: {integrity: sha512-V0f0lWt/PZZ0ZDTz47qdvf4vQ5v0W2EZwhZlE2DTSiQ2U5hLAZhXKwCoM6T0nN+mviplQshNWBenbI6HS1RKgg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tag-picker@9.6.1':
    resolution: {integrity: sha512-eQJHWpc8IfA/D/tsJZ2LOrPsm3CykRrRwIOl4qmRpxGF7jpjc9TTgv/x65xhNAV1zlHkn/kdeF3c6fg51ZPZYQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tags@9.6.1':
    resolution: {integrity: sha512-h511CaowCakh1jXWFk7J2iy/7iXie0EafJqSYkES0fD/3whJOdos355veYkUqdD8G7BaMjL5n9Bkj3OjlxrMJw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-teaching-popover@9.5.1':
    resolution: {integrity: sha512-4YUcfbu/y2uY/gJGwo8EwcqegGBaFc6Mt4pKHLgUJd3m+26YDuHFEwpWEN/gHZ1nKsAXg/zlPpaPuDOwzFZFtQ==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-text@9.5.0':
    resolution: {integrity: sha512-mT//jeZDafU2zEBkSsRjLWtwJ6jyj/f5DPRZQ7/sA9yeQ4YDoXoJ2+x5IoG4VX4tkK1CRvmR4LA/V8JvrWjVyg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-textarea@9.5.1':
    resolution: {integrity: sha512-wGl2rHdv1ZONOSyIjjjbTI/SDRKV89rWF6yVS2qcCI5TFC5SoxadqG+u/9Fuy3kpv69WwRU8Op3mDSz+GYFa/A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-theme@9.1.24':
    resolution: {integrity: sha512-OhVKYD7CMYHxzJEn4PtIszledj8hbQJNWBMfIZsp4Sytdp9vCi0txIQUx4BhS1WqtQPhNGCF16eW9Q3NRrnIrQ==}

  '@fluentui/react-toast@9.5.0':
    resolution: {integrity: sha512-TPgNNxfP5X80Pl/H7jVgreGKfDdEkN/L6G1rnjM18emsIw0DYB+B46JoBwmrPCvISZJNnLstSftwwDSqQEO2hg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-toolbar@9.5.1':
    resolution: {integrity: sha512-8lI8lrRMdm3q9K31iKrOXbC+65OnSi+GtO06FjcKd413x0fBAYbWweRciAh3IyIAiU38RdjIvLKiIs92TuqUpg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tooltip@9.7.1':
    resolution: {integrity: sha512-LiIQDOGEsGeuAbiQItOL/OvSiX9gY5wKgUCduv1cSqQ2J/f3FbsPudBlQJs8UhukdT1jTqF7sjoNel6rMg/rNQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tree@9.11.1':
    resolution: {integrity: sha512-ORRyUoDZzo0GOmiZKwnFlompCjVDi++5tBzf0o/8YQ0xOIlyuCp12oK0UI0AKATXC3lldTupmk0XSorbI4z4qg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-utilities@9.21.0':
    resolution: {integrity: sha512-xViS1WwKIdPza+syMsfh1i3hNgssWgLtbevEeGb6DS/q13UKXaw9P/vezPUs6kSolnSD/juWZGP6u8ytkI1W7g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-virtualizer@9.0.0-alpha.96':
    resolution: {integrity: sha512-0o9RSTAAIoJ4xdM2g8hF5u98Up0OHRknRhMolZHZDoqXEvhJ5GroGtp+NPfU7LxU+dxHrZLx9gQ6wVWe/35ZzQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-virtualizer@9.0.0-alpha.98':
    resolution: {integrity: sha512-BXLXsQPOS+IXrOoH0ZFBbEH6HI7zwGjWoiCPCkqexQYa54flDI8jo2xU7FrvYKVLVNK5oa+UA9jxw5GqDah8QQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-window-provider@2.2.30':
    resolution: {integrity: sha512-2SXuiZcU29W0D9zfExcTfzVx97OI50YCn5fGGO0bTDuP5VxzTQp1mipAY4qm/yJMMinoXkzBGLl1rK0Tdtxh1w==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'

  '@fluentui/react@8.123.0':
    resolution: {integrity: sha512-ZYZF/YDhnx9gUlsJMxj1DvDRjOsD46HteYAJVbYr5ORfgAk+RpPRnBZXPwFqyJr3IDf9WtCJp2jn2Ahxtm/5oA==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/set-version@8.2.24':
    resolution: {integrity: sha512-8uNi2ThvNgF+6d3q2luFVVdk/wZV0AbRfJ85kkvf2+oSRY+f6QVK0w13vMorNhA5puumKcZniZoAfUF02w7NSg==}

  '@fluentui/style-utilities@8.12.2':
    resolution: {integrity: sha512-inDXaSs3Fj+oM5vIlqws9sQa+zh0Nk1+ZGFGCxsVw9jSGT2OD08Hx5oh4hio3wWqDUa78zZ6VEzGChTaLyOm4g==}

  '@fluentui/theme@2.6.67':
    resolution: {integrity: sha512-+9+VkIkZ+NCQDXFP6+WV2ChAj/KHphOEDCvGO15w8ql7sqRxeRQACtoWYNq1tAAsodbnq/amCfo2PNy2VIcIOQ==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'

  '@fluentui/tokens@1.0.0-alpha.21':
    resolution: {integrity: sha512-xQ1T56sNgDFGl+kJdIwhz67mHng8vcwO7Dvx5Uja4t+NRULQBgMcJ4reUo4FGF3TjufHj08pP0/OnKQgnOaSVg==}

  '@fluentui/utilities@8.15.22':
    resolution: {integrity: sha512-ZhDO+6dVLjf2BbHizCA2bnVFLPmOSpemsTMtEY/Nr5fhot5+xeoZVBJrr10X6wN0fTdfMketj/+rnkh5hWXljg==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'

  '@griffel/core@1.19.2':
    resolution: {integrity: sha512-WkB/QQkjy9dE4vrNYGhQvRRUHFkYVOuaznVOMNTDT4pS9aTJ9XPrMTXXlkpcwaf0D3vNKoerj4zAwnU2lBzbOg==}

  '@griffel/react@1.5.30':
    resolution: {integrity: sha512-1q4ojbEVFY5YA0j1NamP0WWF4BKh+GHsVugltDYeEgEaVbH3odJ7tJabuhQgY+7Nhka0pyEFWSiHJev0K3FSew==}
    peerDependencies:
      react: '>=16.8.0 <20.0.0'

  '@griffel/style-types@1.3.0':
    resolution: {integrity: sha512-bHwD3sUE84Xwv4dH011gOKe1jul77M1S6ZFN9Tnq8pvZ48UMdY//vtES6fv7GRS5wXYT4iqxQPBluAiYAfkpmw==}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.3':
    resolution: {integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==}
    engines: {node: '>=18.18'}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.6':
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@microsoft/load-themed-styles@1.10.295':
    resolution: {integrity: sha512-W+IzEBw8a6LOOfRJM02dTT7BDZijxm+Z7lhtOAz1+y9vQm1Kdz9jlAO+qCEKsfxtUOmKilW8DIRqFw2aUgKeGg==}

  '@next/env@14.1.0':
    resolution: {integrity: sha512-Py8zIo+02ht82brwwhTg36iogzFqGLPXlRGKQw5s+qP/kMNc4MAyDeEwBKDijk6zTIbegEgu8Qy7C1LboslQAw==}

  '@next/eslint-plugin-next@15.3.4':
    resolution: {integrity: sha512-lBxYdj7TI8phbJcLSAqDt57nIcobEign5NYIKCiy0hXQhrUbTqLqOaSDi568U6vFg4hJfBdZYsG4iP/uKhCqgg==}

  '@next/swc-darwin-arm64@14.1.0':
    resolution: {integrity: sha512-nUDn7TOGcIeyQni6lZHfzNoo9S0euXnu0jhsbMOmMJUBfgsnESdjN97kM7cBqQxZa8L/bM9om/S5/1dzCrW6wQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@next/swc-darwin-x64@14.1.0':
    resolution: {integrity: sha512-1jgudN5haWxiAl3O1ljUS2GfupPmcftu2RYJqZiMJmmbBT5M1XDffjUtRUzP4W3cBHsrvkfOFdQ71hAreNQP6g==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@next/swc-linux-arm64-gnu@14.1.0':
    resolution: {integrity: sha512-RHo7Tcj+jllXUbK7xk2NyIDod3YcCPDZxj1WLIYxd709BQ7WuRYl3OWUNG+WUfqeQBds6kvZYlc42NJJTNi4tQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-arm64-musl@14.1.0':
    resolution: {integrity: sha512-v6kP8sHYxjO8RwHmWMJSq7VZP2nYCkRVQ0qolh2l6xroe9QjbgV8siTbduED4u0hlk0+tjS6/Tuy4n5XCp+l6g==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-x64-gnu@14.1.0':
    resolution: {integrity: sha512-zJ2pnoFYB1F4vmEVlb/eSe+VH679zT1VdXlZKX+pE66grOgjmKJHKacf82g/sWE4MQ4Rk2FMBCRnX+l6/TVYzQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@next/swc-linux-x64-musl@14.1.0':
    resolution: {integrity: sha512-rbaIYFt2X9YZBSbH/CwGAjbBG2/MrACCVu2X0+kSykHzHnYH5FjHxwXLkcoJ10cX0aWCEynpu+rP76x0914atg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@next/swc-win32-arm64-msvc@14.1.0':
    resolution: {integrity: sha512-o1N5TsYc8f/HpGt39OUQpQ9AKIGApd3QLueu7hXk//2xq5Z9OxmV6sQfNp8C7qYmiOlHYODOGqNNa0e9jvchGQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@next/swc-win32-ia32-msvc@14.1.0':
    resolution: {integrity: sha512-XXIuB1DBRCFwNO6EEzCTMHT5pauwaSj4SWs7CYnME57eaReAKBXCnkUE80p/pAZcewm7hs+vGvNqDPacEXHVkw==}
    engines: {node: '>= 10'}
    cpu: [ia32]
    os: [win32]

  '@next/swc-win32-x64-msvc@14.1.0':
    resolution: {integrity: sha512-9WEbVRRAqJ3YFVqEZIxUqkiO8l1nool1LmNxygr5HWF8AcSYsEpneUDhmjUVJEzO2A04+oPtZdombzzPPkTtgg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@opentelemetry/api@1.9.0':
    resolution: {integrity: sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==}
    engines: {node: '>=8.0.0'}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@pkgr/core@0.2.7':
    resolution: {integrity: sha512-YLT9Zo3oNPJoBjBc4q8G2mjU4tqIbf5CEOORbUUr48dCD9q3umJ3IPlVqOqDakPfd2HuwccBaqlGhN4Gmr5OWg==}
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}

  '@popperjs/core@2.11.8':
    resolution: {integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==}

  '@rollup/rollup-linux-x64-gnu@4.40.0':
    resolution: {integrity: sha512-RcDGMtqF9EFN8i2RYN2W+64CdHruJ5rPqrlYw+cgM3uOVPSsnAQps7cpjXe9be/yDp8UC7VLoCoKC8J3Kn2FkQ==}
    cpu: [x64]
    os: [linux]

  '@rtsao/scc@1.1.0':
    resolution: {integrity: sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==}

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==}

  '@swc/helpers@0.5.2':
    resolution: {integrity: sha512-E4KcWTpoLHqwPHLxidpOqQbcrZVgi0rsmmZXUle1jXmJfuIf/UWpczUJ7MZZ5tlxytgJXyp0w4PGkkeLiuIdZw==}

  '@types/axios@0.9.36':
    resolution: {integrity: sha512-NLOpedx9o+rxo/X5ChbdiX6mS1atE4WHmEEIcR9NLenRVa5HoVjAvjafwU3FPTqnZEstpoqCaW7fagqSoTDNeg==}

  '@types/eslint-scope@3.7.7':
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==}

  '@types/eslint@9.6.1':
    resolution: {integrity: sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==}

  '@types/estree@1.0.8':
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  '@types/events@3.0.3':
    resolution: {integrity: sha512-trOc4AAUThEz9hapPtSd7wf5tiQKvTtu5b371UxXdTuqzIh0ArcRspRP0i0Viu+LXstIQ1z96t1nsPxT9ol01g==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/json5@0.0.29':
    resolution: {integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==}

  '@types/lodash.mergewith@4.6.9':
    resolution: {integrity: sha512-fgkoCAOF47K7sxrQ7Mlud2TH023itugZs2bUg8h/KzT+BnZNrR2jAOmaokbLunHNnobXVWOezAeNn/lZqwxkcw==}

  '@types/lodash@4.17.17':
    resolution: {integrity: sha512-RRVJ+J3J+WmyOTqnz3PiBLA501eKwXl2noseKOrNo/6+XEHjTAxO4xHvxQB6QuNm+s4WRbn6rSiap8+EA+ykFQ==}

  '@types/node@20.5.7':
    resolution: {integrity: sha512-dP7f3LdZIysZnmvP3ANJYTSwg+wLLl8p7RqniVlV7j+oXSXAbt9h0WIBFmJy5inWZoX9wZN6eXx+YXd9Rh3RBA==}

  '@types/parse-json@4.0.2':
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==}

  '@types/prop-types@15.7.15':
    resolution: {integrity: sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==}

  '@types/react-dom@19.1.6':
    resolution: {integrity: sha512-4hOiT/dwO8Ko0gV1m/TJZYk3y0KBnY9vzDh7W+DH17b2HFSOGgdj33dhihPeuy3l0q23+4e+hoXHV6hCC4dCXw==}
    peerDependencies:
      '@types/react': ^19.0.0

  '@types/react@18.3.23':
    resolution: {integrity: sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==}

  '@types/react@19.1.5':
    resolution: {integrity: sha512-piErsCVVbpMMT2r7wbawdZsq4xMvIAhQuac2gedQHysu1TZYEigE6pnFfgZT+/jQnrRuF5r+SHzuehFjfRjr4g==}

  '@types/trusted-types@2.0.7':
    resolution: {integrity: sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==}

  '@types/webrtc@0.0.37':
    resolution: {integrity: sha512-JGAJC/ZZDhcrrmepU4sPLQLIOIAgs5oIK+Ieq90K8fdaNMhfdfqmYatJdgif1NDQtvrSlTOGJDUYHIDunuufOg==}

  '@typescript-eslint/eslint-plugin@8.35.0':
    resolution: {integrity: sha512-ijItUYaiWuce0N1SoSMrEd0b6b6lYkYt99pqCPfybd+HKVXtEvYhICfLdwp42MhiI5mp0oq7PKEL+g1cNiz/Eg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.35.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/parser@8.35.0':
    resolution: {integrity: sha512-6sMvZePQrnZH2/cJkwRpkT7DxoAWh+g6+GFRK6bV3YQo7ogi3SX5rgF6099r5Q53Ma5qeT7LGmOmuIutF4t3lA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/project-service@8.35.0':
    resolution: {integrity: sha512-41xatqRwWZuhUMF/aZm2fcUsOFKNcG28xqRSS6ZVr9BVJtGExosLAm5A1OxTjRMagx8nJqva+P5zNIGt8RIgbQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/scope-manager@8.35.0':
    resolution: {integrity: sha512-+AgL5+mcoLxl1vGjwNfiWq5fLDZM1TmTPYs2UkyHfFhgERxBbqHlNjRzhThJqz+ktBqTChRYY6zwbMwy0591AA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/tsconfig-utils@8.35.0':
    resolution: {integrity: sha512-04k/7247kZzFraweuEirmvUj+W3bJLI9fX6fbo1Qm2YykuBvEhRTPl8tcxlYO8kZZW+HIXfkZNoasVb8EV4jpA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/type-utils@8.35.0':
    resolution: {integrity: sha512-ceNNttjfmSEoM9PW87bWLDEIaLAyR+E6BoYJQ5PfaDau37UGca9Nyq3lBk8Bw2ad0AKvYabz6wxc7DMTO2jnNA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/types@8.35.0':
    resolution: {integrity: sha512-0mYH3emanku0vHw2aRLNGqe7EXh9WHEhi7kZzscrMDf6IIRUQ5Jk4wp1QrledE/36KtdZrVfKnE32eZCf/vaVQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.35.0':
    resolution: {integrity: sha512-F+BhnaBemgu1Qf8oHrxyw14wq6vbL8xwWKKMwTMwYIRmFFY/1n/9T/jpbobZL8vp7QyEUcC6xGrnAO4ua8Kp7w==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/utils@8.35.0':
    resolution: {integrity: sha512-nqoMu7WWM7ki5tPgLVsmPM8CkqtoPUG6xXGeefM5t4x3XumOEKMoUZPdi+7F+/EotukN4R9OWdmDxN80fqoZeg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/visitor-keys@8.35.0':
    resolution: {integrity: sha512-zTh2+1Y8ZpmeQaQVIc/ZZxsx8UzgKJyNg1PTvjzC7WMhPSVS8bfDX34k1SrwOf016qd5RU3az2UxUNue3IfQ5g==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typespec/ts-http-runtime@0.2.3':
    resolution: {integrity: sha512-oRhjSzcVjX8ExyaF8hC0zzTqxlVuRlgMHL/Bh4w3xB9+wjbm0FpXylVU/lBrn+kgphwYTrOk3tp+AVShGmlYCg==}
    engines: {node: '>=18.0.0'}

  '@webassemblyjs/ast@1.14.1':
    resolution: {integrity: sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==}

  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    resolution: {integrity: sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==}

  '@webassemblyjs/helper-api-error@1.13.2':
    resolution: {integrity: sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==}

  '@webassemblyjs/helper-buffer@1.14.1':
    resolution: {integrity: sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==}

  '@webassemblyjs/helper-numbers@1.13.2':
    resolution: {integrity: sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==}

  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    resolution: {integrity: sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    resolution: {integrity: sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==}

  '@webassemblyjs/ieee754@1.13.2':
    resolution: {integrity: sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==}

  '@webassemblyjs/leb128@1.13.2':
    resolution: {integrity: sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==}

  '@webassemblyjs/utf8@1.13.2':
    resolution: {integrity: sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==}

  '@webassemblyjs/wasm-edit@1.14.1':
    resolution: {integrity: sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==}

  '@webassemblyjs/wasm-gen@1.14.1':
    resolution: {integrity: sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==}

  '@webassemblyjs/wasm-opt@1.14.1':
    resolution: {integrity: sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==}

  '@webassemblyjs/wasm-parser@1.14.1':
    resolution: {integrity: sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==}

  '@webassemblyjs/wast-printer@1.14.1':
    resolution: {integrity: sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==}

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}

  '@zag-js/dom-query@0.31.1':
    resolution: {integrity: sha512-oiuohEXAXhBxpzzNm9k2VHGEOLC1SXlXSbRPcfBZ9so5NRQUA++zCE7cyQJqGLTZR0t3itFLlZqDbYEXRrefwg==}

  '@zag-js/element-size@0.31.1':
    resolution: {integrity: sha512-4T3yvn5NqqAjhlP326Fv+w9RqMIBbNN9H72g5q2ohwzhSgSfZzrKtjL4rs9axY/cw9UfMfXjRjEE98e5CMq7WQ==}

  '@zag-js/focus-visible@0.31.1':
    resolution: {integrity: sha512-dbLksz7FEwyFoANbpIlNnd3bVm0clQSUsnP8yUVQucStZPsuWjCrhL2jlAbGNrTrahX96ntUMXHb/sM68TibFg==}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  agent-base@5.1.1:
    resolution: {integrity: sha512-TMeqbNl2fMW0nMjTEPOwe3J/PRFP4vqeoNuQMG0HlMrtm5QxKqdvAkZ1pRBQ/ulIyDD5Yq0nJ7YbdD8ey0TO3g==}
    engines: {node: '>= 6.0.0'}

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  agent-base@7.1.3:
    resolution: {integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==}
    engines: {node: '>= 14'}

  ajv-formats@2.1.1:
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-keywords@5.1.0:
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ajv@8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  aria-hidden@1.2.6:
    resolution: {integrity: sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA==}
    engines: {node: '>=10'}

  array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==}
    engines: {node: '>= 0.4'}

  array-includes@3.1.9:
    resolution: {integrity: sha512-FmeCCAenzH0KH381SPT5FZmiA/TmpndpcaShhfgEN9eCVjnFBqq3l1xrI42y8+PPLI6hypzou4GXw00WHmPBLQ==}
    engines: {node: '>= 0.4'}

  array.prototype.findlast@1.2.5:
    resolution: {integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==}
    engines: {node: '>= 0.4'}

  array.prototype.findlastindex@1.2.6:
    resolution: {integrity: sha512-F/TKATkzseUExPlfvmwQKGITM3DGTK+vkAsCZoDc5daVygbJBnjEUCbgkAvVFsgfXfX4YIqZ/27G3k3tdXrTxQ==}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.3:
    resolution: {integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.3:
    resolution: {integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.4:
    resolution: {integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==}
    engines: {node: '>= 0.4'}

  async-function@1.0.0:
    resolution: {integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==}
    engines: {node: '>= 0.4'}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  autoprefixer@10.4.19:
    resolution: {integrity: sha512-BaENR2+zBZ8xXhM4pUaKUxlVdxZ0EZhjvbopwnXmxRUfqDmwSpC2lAi/QXvx7NRdPCo1WKEcEF6mV64si1z4Ew==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}

  axios@1.10.0:
    resolution: {integrity: sha512-/1xYAC4MP/HEG+3duIhFr4ZQXR4sQXOIe+o6sdqzeykGLx6Upp/1p8MHqhINOvGeP7xyNHe7tsiJByc4SSVUxw==}

  babel-loader@10.0.0:
    resolution: {integrity: sha512-z8jt+EdS61AMw22nSfoNJAZ0vrtmhPRVi6ghL3rCeRZI8cdNYFiV5xeV3HbE7rlZZNmGH8BVccwWt8/ED0QOHA==}
    engines: {node: ^18.20.0 || ^20.10.0 || >=22.0.0}
    peerDependencies:
      '@babel/core': ^7.12.0
      webpack: '>=5.61.0'

  babel-plugin-macros@3.1.0:
    resolution: {integrity: sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==}
    engines: {node: '>=10', npm: '>=6'}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  bent@7.3.12:
    resolution: {integrity: sha512-T3yrKnVGB63zRuoco/7Ybl7BwwGZR0lceoVG5XmQyMIH9s19SV5m+a8qam4if0zQuAmOQTyPTPmsQBdAorGK3w==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  brace-expansion@1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}

  brace-expansion@2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browserslist@4.25.0:
    resolution: {integrity: sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  busboy@1.6.0:
    resolution: {integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==}
    engines: {node: '>=10.16.0'}

  bytesish@0.4.4:
    resolution: {integrity: sha512-i4uu6M4zuMUiyfZN4RU2+i9+peJh//pXhd9x1oSe1LBkZ3LEbCoygu8W0bXTukU1Jme2txKuotpCZRaC3FLxcQ==}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase-css@2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}

  caniuse-lite@1.0.30001723:
    resolution: {integrity: sha512-1R/elMjtehrFejxwmexeXAtae5UO9iSyFn6G/I806CYC/BLyyBk1EPhrKBkWhy6wM6Xnm47dSJQec+tLJ39WHw==}

  caseless@0.12.0:
    resolution: {integrity: sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  chrome-trace-event@1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}

  client-only@0.0.1:
    resolution: {integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color2k@2.0.3:
    resolution: {integrity: sha512-zW190nQTIoXcGCaU08DvVNFTmQhUpnJfVuAKfWqUQkflXKpaDdpaYoM0iluLS9lgJNHyBF58KKA2FBEwkD7wog==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  concurrently@8.2.2:
    resolution: {integrity: sha512-1dP4gpXFhei8IOtlXRE/T/4H88ElHgTiUzh71YUmtjTEHMSRS2Z/fgOxHSxxusGHogsRfxNq1vyAwxSC+EVyDg==}
    engines: {node: ^14.13.0 || >=16.0.0}
    hasBin: true

  convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  copy-to-clipboard@3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}

  cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  data-view-buffer@1.0.2:
    resolution: {integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.2:
    resolution: {integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.1:
    resolution: {integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==}
    engines: {node: '>= 0.4'}

  date-fns@2.30.0:
    resolution: {integrity: sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==}
    engines: {node: '>=0.11'}

  debug@3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  detect-node-es@1.1.0:
    resolution: {integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==}

  didyoumean@1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}

  dlv@1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}

  doctrine@2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}

  dom-helpers@5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}

  dom-serializer@2.0.0:
    resolution: {integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==}

  domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  domhandler@5.0.3:
    resolution: {integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==}
    engines: {node: '>= 4'}

  dompurify@3.2.6:
    resolution: {integrity: sha512-/2GogDQlohXPZe6D6NOgQvXLPSYBqIWMnZ8zzOhn09REE4eyAzb+Hed3jhoM9OkuaJ8P6ZGTTVWQKAi8ieIzfQ==}

  domutils@3.2.2:
    resolution: {integrity: sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==}

  dotenv@16.5.0:
    resolution: {integrity: sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==}
    engines: {node: '>=12'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  electron-to-chromium@1.5.167:
    resolution: {integrity: sha512-LxcRvnYO5ez2bMOFpbuuVuAI5QNeY1ncVytE/KXaL6ZNfzX1yPlAO0nSOyIHx2fVAuUprMqPs/TdVhUFZy7SIQ==}

  embla-carousel-autoplay@8.6.0:
    resolution: {integrity: sha512-OBu5G3nwaSXkZCo1A6LTaFMZ8EpkYbwIaH+bPqdBnDGQ2fh4+NbzjXjs2SktoPNKCtflfVMc75njaDHOYXcrsA==}
    peerDependencies:
      embla-carousel: 8.6.0

  embla-carousel-fade@8.6.0:
    resolution: {integrity: sha512-qaYsx5mwCz72ZrjlsXgs1nKejSrW+UhkbOMwLgfRT7w2LtdEB03nPRI06GHuHv5ac2USvbEiX2/nAHctcDwvpg==}
    peerDependencies:
      embla-carousel: 8.6.0

  embla-carousel@8.6.0:
    resolution: {integrity: sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==}
    engines: {node: '>=10.13.0'}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  entities@6.0.1:
    resolution: {integrity: sha512-aN97NXWF6AWBTahfVOIrB/NShkzi5H7F9r1s9mD3cDj4Ko5f2qhhVoYMibXF7GlLveb/D2ioWay8lxI97Ven3g==}
    engines: {node: '>=0.12'}

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  es-abstract@1.24.0:
    resolution: {integrity: sha512-WSzPgsdLtTcQwm4CROfS5ju2Wa1QQcVeT37jFjYzdFz1r9ahadC8B8/a4qxJxM+09F18iumCdRmlr96ZYkQvEg==}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-iterator-helpers@1.2.1:
    resolution: {integrity: sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.7.0:
    resolution: {integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.1.0:
    resolution: {integrity: sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==}
    engines: {node: '>= 0.4'}

  es-to-primitive@1.3.0:
    resolution: {integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==}
    engines: {node: '>= 0.4'}

  esbuild@0.25.5:
    resolution: {integrity: sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  eslint-config-prettier@10.1.5:
    resolution: {integrity: sha512-zc1UmCpNltmVY34vuLRV61r1K27sWuX39E+uyUnY8xS2Bex88VV9cugG+UZbRSRGtGyFboj+D8JODyme1plMpw==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-import-resolver-node@0.3.9:
    resolution: {integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==}

  eslint-module-utils@2.12.0:
    resolution: {integrity: sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-es@3.0.1:
    resolution: {integrity: sha512-GUmAsJaN4Fc7Gbtl8uOBlayo2DqhwWvEzykMHSCZHU3XdJ+NSzzZcVhXh3VxX5icqQ+oQdIEawXX8xkR3mIFmQ==}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=4.19.1'

  eslint-plugin-import@2.31.0:
    resolution: {integrity: sha512-ixmkI62Rbc2/w8Vfxyh1jQRTdRTF52VxwRVHl/ykPAmqG+Nb7/kNn+byLP0LxPgI7zWA16Jt82SybJInmMia3A==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-node@11.1.0:
    resolution: {integrity: sha512-oUwtPJ1W0SKD0Tr+wqu92c5xuCeQqB3hSCHasn/ZgjFdA9iDGNkNf2Zi9ztY7X+hNuMib23LNGRm6+uN+KLE3g==}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=5.16.0'

  eslint-plugin-prettier@5.5.1:
    resolution: {integrity: sha512-dobTkHT6XaEVOo8IO90Q4DOSxnm3Y151QxPJlM/vKC0bVy+d6cVWQZLlFiuZPP0wS6vZwSKeJgKkcS+KfMBlRw==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '>= 7.0.0 <10.0.0 || >=10.1.0'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true

  eslint-plugin-react-hooks@5.2.0:
    resolution: {integrity: sha512-+f15FfK64YQwZdJNELETdn5ibXEUQmW1DZL6KXhNnc2heoy/sg9VJJeT7n8TlMWouzWqSWavFkIhHyIbIAEapg==}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0

  eslint-plugin-react@7.37.5:
    resolution: {integrity: sha512-Qteup0SqU15kdocexFNAJMvCJEfa2xUKNV4CC1xsVMrIIqEy3SQ/rqyxCWNzfrd3/ldy6HMlD2e0JDVpDg2qIA==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-plugin-unused-imports@4.1.4:
    resolution: {integrity: sha512-YptD6IzQjDardkl0POxnnRBhU1OEePMV0nd6siHaRBbd+lyh6NAhFEobiznKU7kTsSsDeSD62Pe7kAM1b7dAZQ==}
    peerDependencies:
      '@typescript-eslint/eslint-plugin': ^8.0.0-0 || ^7.0.0 || ^6.0.0 || ^5.0.0
      eslint: ^9.0.0 || ^8.0.0
    peerDependenciesMeta:
      '@typescript-eslint/eslint-plugin':
        optional: true

  eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}

  eslint-scope@8.4.0:
    resolution: {integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-utils@2.1.0:
    resolution: {integrity: sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==}
    engines: {node: '>=6'}

  eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==}
    engines: {node: '>=4'}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.1:
    resolution: {integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.29.0:
    resolution: {integrity: sha512-GsGizj2Y1rCWDu6XoEekL3RLilp0voSePurjZIkxL3wlm5o5EC9VpgaP7lrCvjnkuLvzFBQWB3vWB3K5KQTveQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.4.0:
    resolution: {integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-diff@1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==}

  fast-glob@3.3.1:
    resolution: {integrity: sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==}
    engines: {node: '>=8.6.0'}

  fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fast-uri@3.0.6:
    resolution: {integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==}

  fastq@1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==}
    engines: {node: '>=16.0.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  find-root@1.1.0:
    resolution: {integrity: sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==}
    engines: {node: '>=16'}

  flatted@3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==}

  focus-lock@1.3.6:
    resolution: {integrity: sha512-Ik/6OCk9RQQ0T5Xw+hKNLWrjSMtv51dD4GRmJjbD5a58TIEpI5a5iXagKVl3Z5UuyslMCA8Xwnu76jQob62Yhg==}
    engines: {node: '>=10'}

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.5:
    resolution: {integrity: sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==}
    engines: {node: '>= 0.4'}

  foreground-child@3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}

  form-data@4.0.3:
    resolution: {integrity: sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==}
    engines: {node: '>= 6'}

  fraction.js@4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}

  framer-motion@11.18.2:
    resolution: {integrity: sha512-5F5Och7wrvtLVElIpclDT0CBzMVg3dL22B64aZwHtsIY8RB4mXICLrkajK4G9R+ieSAGcgrLeae2SeUTg2pr6w==}
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  framer-motion@12.18.1:
    resolution: {integrity: sha512-6o4EDuRPLk4LSZ1kRnnEOurbQ86MklVk+Y1rFBUKiF+d2pCdvMjWVu0ZkyMVCTwl5UyTH2n/zJEJx+jvTYuxow==}
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  framesync@6.1.2:
    resolution: {integrity: sha512-jBTqhX6KaQVDyus8muwZbBeGGP0XgujBRbQ7gM7BRdS3CadCZIHiawyzYLnafYcvZIh5j8WE7cxZKFn7dXhu9g==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  function.prototype.name@1.1.8:
    resolution: {integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==}
    engines: {node: '>= 0.4'}

  functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-nonce@1.0.1:
    resolution: {integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==}
    engines: {node: '>=6'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  get-symbol-description@1.1.0:
    resolution: {integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==}
    engines: {node: '>= 0.4'}

  get-them-args@1.3.2:
    resolution: {integrity: sha512-LRn8Jlk+DwZE4GTlDbT3Hikd1wSHgLMme/+7ddlqKd7ldwR6LjJgTVWzBnR01wnYGe4KgrXjg287RaI22UHmAw==}

  get-tsconfig@4.10.1:
    resolution: {integrity: sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  glob@10.3.10:
    resolution: {integrity: sha512-fa46+tv1Ak0UPK1TOy/pZrIybNNt4HCv7SDzwyfiOZkvZLEbjsZkJBPtDHVshZjbecAoAGSC20MjLDG/qr679g==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==}
    engines: {node: '>=18'}

  globalthis@1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  has-bigints@1.1.0:
    resolution: {integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==}
    engines: {node: '>= 0.4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-proto@1.2.0:
    resolution: {integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}

  html-dom-parser@5.1.1:
    resolution: {integrity: sha512-+o4Y4Z0CLuyemeccvGN4bAO20aauB2N9tFEAep5x4OW34kV4PTarBHm6RL02afYt2BMKcr0D2Agep8S3nJPIBg==}

  html-react-parser@5.2.5:
    resolution: {integrity: sha512-bRPdv8KTqG9CEQPMNGksDqmbiRfVQeOidry8pVetdh/1jQ1Edx4KX5m0lWvDD89Pt4CqTYjK1BLz6NoNVxN/Uw==}
    peerDependencies:
      '@types/react': 0.14 || 15 || 16 || 17 || 18 || 19
      react: 0.14 || 15 || 16 || 17 || 18 || 19
    peerDependenciesMeta:
      '@types/react':
        optional: true

  htmlparser2@10.0.0:
    resolution: {integrity: sha512-TwAZM+zE5Tq3lrEHvOlvwgj1XLWQCtaaibSN11Q+gGBAS7Y1uZSWwXXRe4iF6OXnaq1riyQAPFOBtYc77Mxq0g==}

  http-proxy-agent@7.0.2:
    resolution: {integrity: sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==}
    engines: {node: '>= 14'}

  https-proxy-agent@4.0.0:
    resolution: {integrity: sha512-zoDhWrkR3of1l9QAL8/scJZyLu8j/gBkcwcaQOZh7Gyh/+uJQzGVETdgT30akuwkpL8HTRfssqI3BZuV18teDg==}
    engines: {node: '>= 6.0.0'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==}
    engines: {node: '>= 14'}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  ignore@7.0.5:
    resolution: {integrity: sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==}
    engines: {node: '>= 4'}

  immer@10.1.1:
    resolution: {integrity: sha512-s2MPrmjovJcoMaHtx6K11Ra7oD05NT97w1IC5zpMkT6Atjr7H8LjaDd81iIxUYpMKSRRNMJE703M1Fhr/TctHw==}

  import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  inline-style-parser@0.2.4:
    resolution: {integrity: sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==}

  internal-slot@1.1.0:
    resolution: {integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.5:
    resolution: {integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-async-function@2.1.1:
    resolution: {integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==}
    engines: {node: '>= 0.4'}

  is-bigint@1.1.0:
    resolution: {integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==}
    engines: {node: '>= 0.4'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-boolean-object@1.2.2:
    resolution: {integrity: sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==}
    engines: {node: '>= 0.4'}

  is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}

  is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.2:
    resolution: {integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==}
    engines: {node: '>= 0.4'}

  is-date-object@1.1.0:
    resolution: {integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==}
    engines: {node: '>= 0.4'}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.1.1:
    resolution: {integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-generator-function@1.1.0:
    resolution: {integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-map@2.0.3:
    resolution: {integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==}
    engines: {node: '>= 0.4'}

  is-negative-zero@2.0.3:
    resolution: {integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==}
    engines: {node: '>= 0.4'}

  is-number-object@1.1.1:
    resolution: {integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-regex@1.2.1:
    resolution: {integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==}
    engines: {node: '>= 0.4'}

  is-set@2.0.3:
    resolution: {integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==}
    engines: {node: '>= 0.4'}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-string@1.1.1:
    resolution: {integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==}
    engines: {node: '>= 0.4'}

  is-symbol@1.1.1:
    resolution: {integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.15:
    resolution: {integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==}
    engines: {node: '>= 0.4'}

  is-weakmap@2.0.2:
    resolution: {integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==}
    engines: {node: '>= 0.4'}

  is-weakref@1.1.1:
    resolution: {integrity: sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==}
    engines: {node: '>= 0.4'}

  is-weakset@2.0.4:
    resolution: {integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==}
    engines: {node: '>= 0.4'}

  isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  iterator.prototype@1.1.5:
    resolution: {integrity: sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==}
    engines: {node: '>= 0.4'}

  jackspeak@2.3.6:
    resolution: {integrity: sha512-N3yCS/NegsOBokc8GAdM8UcmfsKiSS8cipheD/nivzr700H+nsMOxJjQnvwOcRYVuFkdH0wGUvW2WbXGmrZGbQ==}
    engines: {node: '>=14'}

  jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}

  jiti@1.21.7:
    resolution: {integrity: sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==}
    hasBin: true

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==}
    engines: {node: '>=4.0'}

  jwt-decode@4.0.0:
    resolution: {integrity: sha512-+KJGIyHgkGuIq3IEBNftfhW/LfWhXUIY6OmyVWjliu5KH1y0fw7VQ8YndE2O4qZdMSd9SqbnC8GOcZEy0Om7sA==}
    engines: {node: '>=18'}

  keyborg@2.6.0:
    resolution: {integrity: sha512-o5kvLbuTF+o326CMVYpjlaykxqYP9DphFQZ2ZpgrvBouyvOxyEB7oqe8nOLFpiV5VCtz0D3pt8gXQYWpLpBnmA==}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  kill-port@2.0.1:
    resolution: {integrity: sha512-e0SVOV5jFo0mx8r7bS29maVWp17qGqLBZ5ricNSajON6//kmb7qqqNnml4twNE8Dtj97UQD+gNFOaipS/q1zzQ==}
    hasBin: true

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  lilconfig@3.1.3:
    resolution: {integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  linkify-it@2.2.0:
    resolution: {integrity: sha512-GnAl/knGn+i1U/wjBz3akz2stz+HrHLsxMwHQGofCDfPvlf+gDKN58UtfmUquTY4/MXeE2x7k19KQmeoZi94Iw==}

  loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.mergewith@4.6.2:
    resolution: {integrity: sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  memoize-one@5.2.1:
    resolution: {integrity: sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  microsoft-cognitiveservices-speech-sdk@1.44.1:
    resolution: {integrity: sha512-D4U7G1iQ6za9RQoXYc9ed5KrnWe3q4XIcWQ4DMqAl60CvOSXxSEJqTmTcVjeWVLY2XM9bp+2KKKyWceByAec8A==}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  motion-dom@11.18.1:
    resolution: {integrity: sha512-g76KvA001z+atjfxczdRtw/RXOM3OMSdd1f4DL77qCTF/+avrRJiawSG4yDibEQ215sr9kpinSlX2pCTJ9zbhw==}

  motion-dom@12.18.1:
    resolution: {integrity: sha512-dR/4EYT23Snd+eUSLrde63Ws3oXQtJNw/krgautvTfwrN/2cHfCZMdu6CeTxVfRRWREW3Fy1f5vobRDiBb/q+w==}

  motion-utils@11.18.1:
    resolution: {integrity: sha512-49Kt+HKjtbJKLtgO/LKj9Ld+6vw9BjH5d9sc40R/kVyH8GLAXgT42M2NnuPcJNuA3s9ZfZBUcwIgpmZWGEE+hA==}

  motion-utils@12.18.1:
    resolution: {integrity: sha512-az26YDU4WoDP0ueAkUtABLk2BIxe28d8NH1qWT8jPGhPyf44XTdDUh8pDk9OPphaSrR9McgpcJlgwSOIw/sfkA==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanoid@3.3.8:
    resolution: {integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  next@14.1.0:
    resolution: {integrity: sha512-wlzrsbfeSU48YQBjZhDzOwhWhGsy+uQycR8bHAOt1LY1bn3zZEcDyHQOEoN3aWzQ8LHCAJ1nqrWCc9XF2+O45Q==}
    engines: {node: '>=18.17.0'}
    hasBin: true
    peerDependencies:
      '@opentelemetry/api': ^1.1.0
      react: ^18.2.0
      react-dom: ^18.2.0
      sass: ^1.3.0
    peerDependenciesMeta:
      '@opentelemetry/api':
        optional: true
      sass:
        optional: true

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-hash@3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}

  object-inspect@1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  object.assign@4.1.7:
    resolution: {integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==}
    engines: {node: '>= 0.4'}

  object.entries@1.1.9:
    resolution: {integrity: sha512-8u/hfXFRBD1O0hPUjioLhoWFHRmt6tKA4/vZPyckBr18l1KE9uHrFaFaUi8MDRTpi4uak2goyPTSNJLXX2k2Hw==}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==}
    engines: {node: '>= 0.4'}

  object.groupby@1.0.3:
    resolution: {integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==}
    engines: {node: '>= 0.4'}

  object.values@1.2.1:
    resolution: {integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==}
    engines: {node: '>= 0.4'}

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  own-keys@1.0.1:
    resolution: {integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==}
    engines: {node: '>= 0.4'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}

  pirates@4.0.7:
    resolution: {integrity: sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==}
    engines: {node: '>= 6'}

  possible-typed-array-names@1.1.0:
    resolution: {integrity: sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==}
    engines: {node: '>= 0.4'}

  postcss-import@15.1.0:
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21

  postcss-load-config@4.0.2:
    resolution: {integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-nested@6.2.0:
    resolution: {integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14

  postcss-selector-parser@6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@8.4.31:
    resolution: {integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==}
    engines: {node: ^10 || ^12 || >=14}

  postcss@8.4.38:
    resolution: {integrity: sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==}
    engines: {node: ^10 || ^12 || >=14}

  postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==}
    engines: {node: '>=6.0.0'}

  prettier@3.6.1:
    resolution: {integrity: sha512-5xGWRa90Sp2+x1dQtNpIpeOQpTDBs9cZDmA/qs2vDNN2i18PdapqY7CmBeyLlMuGqXJRIOPaCaVZTLNQRWUH/A==}
    engines: {node: '>=14'}
    hasBin: true

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}

  react-clientside-effect@1.2.8:
    resolution: {integrity: sha512-ma2FePH0z3px2+WOu6h+YycZcEvFmmxIlAb62cF52bG86eMySciO/EQZeQMXd07kPCYB0a1dWDT5J+KE9mCDUw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  react-dom@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1

  react-fast-compare@3.2.2:
    resolution: {integrity: sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==}

  react-focus-lock@2.13.6:
    resolution: {integrity: sha512-ehylFFWyYtBKXjAO9+3v8d0i+cnc1trGS0vlTGhzFW1vbFXVUTmR8s2tt/ZQG8x5hElg6rhENlLG1H3EZK0Llg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-icons@5.5.0:
    resolution: {integrity: sha512-MEFcXdkP3dLo8uumGI5xN3lDFNsRtrjbOEKDLD7yv76v4wpnEq2Lt2qeHaQOr34I/wPN3s3+N08WkQ+CW37Xiw==}
    peerDependencies:
      react: '*'

  react-intersection-observer@9.16.0:
    resolution: {integrity: sha512-w9nJSEp+DrW9KmQmeWHQyfaP6b03v+TdXynaoA964Wxt7mdR3An11z4NNCQgL4gKSK7y1ver2Fq+JKH6CWEzUA==}
    peerDependencies:
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      react-dom:
        optional: true

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}

  react-linkify@1.0.0-alpha:
    resolution: {integrity: sha512-7gcIUvJkAXXttt1fmBK9cwn+1jTa4hbKLGCZ9J1U6EOkyb2/+LKL1Z28d9rtDLMnpvImlNlLPdTPooorl5cpmg==}

  react-property@2.0.2:
    resolution: {integrity: sha512-+PbtI3VuDV0l6CleQMsx2gtK0JZbZKbpdu5ynr+lbsuvtmgbNcS3VM0tuY2QjFNOcWxvXeHjDpy42RO+4U2rug==}

  react-remove-scroll-bar@2.3.8:
    resolution: {integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.7.1:
    resolution: {integrity: sha512-HpMh8+oahmIdOuS5aFKKY6Pyog+FNaZV/XyJOq7b4YFwsFHe5yYfdbIalI4k3vU2nSDql7YskmUseHsRrJqIPA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-style-singleton@2.2.3:
    resolution: {integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-transition-group@4.4.5:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react-use-draggable-scroll@0.4.7:
    resolution: {integrity: sha512-6gCxGPO9WV5dIsBaDrgUKBaac8CY07PkygcArfajijYSNDwAq0girDRjaBuF1+lRqQryoLFQfpVaV2u/Yh6CrQ==}
    engines: {node: '>=10'}
    peerDependencies:
      react: '>=16'

  react@18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==}
    engines: {node: '>=0.10.0'}

  read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  reflect.getprototypeof@1.0.10:
    resolution: {integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==}
    engines: {node: '>= 0.4'}

  regexp.prototype.flags@1.5.4:
    resolution: {integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==}
    engines: {node: '>= 0.4'}

  regexpp@3.2.0:
    resolution: {integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==}
    engines: {node: '>=8'}

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  reselect@4.1.8:
    resolution: {integrity: sha512-ab9EmR80F/zQTMNeneUr4cv+jSwPJgIlvEmVwLerwrWVbpLlBuls9XHzIeTFy4cegU2NHBp3va0LKOzU5qFEYQ==}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==}

  resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true

  resolve@2.0.0-next.5:
    resolution: {integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==}
    hasBin: true

  reusify@1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  roosterjs-content-model-api@9.29.3:
    resolution: {integrity: sha512-jg6EjK1j+owo8HFMAasn1qxeLns6OonH+5zJhuJt+AI44NuuKz+IufIVBMrrLu0toaIwucAy6+yKCc82qSJFng==}

  roosterjs-content-model-core@9.29.3:
    resolution: {integrity: sha512-8jfUNlmrkV7CoUKXNNpyN1GCibdoKS6V23se78az9MuFLKJaCV/GhxatdLKjzDzHsx2hQbJKxd/GvHnEvr7VQQ==}

  roosterjs-content-model-dom@9.29.3:
    resolution: {integrity: sha512-hbfo7Gn5N14kPsTLbKcmSepOALgo7n6CkSlqi6Nw5vFpiLndpUE8//OTQQx+hngZGrez06WSMskUl9aZV06qhg==}

  roosterjs-content-model-plugins@9.29.3:
    resolution: {integrity: sha512-1rAtlHiWgJRQai67vSgVvNALC0AMut3FqZInTgkUy7j1AxCFar7bjUu9nEuvJGmZtrWc2+nPRvycKNKM6bYLJQ==}

  roosterjs-content-model-types@9.29.3:
    resolution: {integrity: sha512-y18iK+GboTCwGsQYLqFBX0nQ3x/TaTc4Db6HxNLWdyLUykSVf9ycRngpmzOf321qZDA6dqMyLUo3a6vpxYTt0Q==}

  rtl-css-js@1.16.1:
    resolution: {integrity: sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  rxjs@7.8.2:
    resolution: {integrity: sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==}

  safe-array-concat@1.1.3:
    resolution: {integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==}
    engines: {node: '>=0.4'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safe-push-apply@1.0.0:
    resolution: {integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==}
    engines: {node: '>= 0.4'}

  safe-regex-test@1.1.0:
    resolution: {integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==}
    engines: {node: '>= 0.4'}

  scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}

  schema-utils@4.3.2:
    resolution: {integrity: sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ==}
    engines: {node: '>= 10.13.0'}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  serialize-javascript@6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}

  set-proto@1.0.0:
    resolution: {integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==}
    engines: {node: '>= 0.4'}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shell-exec@1.0.2:
    resolution: {integrity: sha512-jyVd+kU2X+mWKMmGhx4fpWbPsjvD53k9ivqetutVW/BQ+WIZoDoP4d8vUMGezV6saZsiNoW2f9GIhg9Dondohg==}

  shell-quote@1.8.3:
    resolution: {integrity: sha512-ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw==}
    engines: {node: '>= 0.4'}

  side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  source-map@0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}

  spawn-command@0.0.2:
    resolution: {integrity: sha512-zC8zGoGkmc8J9ndvml8Xksr1Amk9qBujgbF0JAIWO7kXr43w0h/0GJNM/Vustixu+YE8N/MTrQ7N31FvHUACxQ==}

  stop-iteration-iterator@1.1.0:
    resolution: {integrity: sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==}
    engines: {node: '>= 0.4'}

  streamsearch@1.1.0:
    resolution: {integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==}
    engines: {node: '>=10.0.0'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string.prototype.matchall@4.0.12:
    resolution: {integrity: sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==}
    engines: {node: '>= 0.4'}

  string.prototype.repeat@1.0.0:
    resolution: {integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==}

  string.prototype.trim@1.2.10:
    resolution: {integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.9:
    resolution: {integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==}
    engines: {node: '>= 0.4'}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  style-to-js@1.1.16:
    resolution: {integrity: sha512-/Q6ld50hKYPH3d/r6nr117TZkHR0w0kGGIVfpG9N6D8NymRPM9RqCUv4pRpJ62E5DqOYx2AFpbZMyCPnjQCnOw==}

  style-to-object@1.0.8:
    resolution: {integrity: sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g==}

  styled-jsx@5.1.1:
    resolution: {integrity: sha512-pW7uC1l4mBZ8ugbiZrcIsiIvVx1UmTfw7UkC3Um2tmfUq9Bhk8IiyEIPl6F8agHgjzku6j0xQEZbfA5uSgSaCw==}
    engines: {node: '>= 12.0.0'}
    peerDependencies:
      '@babel/core': '*'
      babel-plugin-macros: '*'
      react: '>= 16.8.0 || 17.x.x || ^18.0.0-0'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      babel-plugin-macros:
        optional: true

  stylis@4.2.0:
    resolution: {integrity: sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==}

  stylis@4.3.6:
    resolution: {integrity: sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==}

  sucrase@3.35.0:
    resolution: {integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  synckit@0.11.8:
    resolution: {integrity: sha512-+XZ+r1XGIJGeQk3VvXhT6xx/VpbHsRzsTkGgF6E5RX9TTXD0118l87puaEBZ566FhqblC6U0d4XnubznJDm30A==}
    engines: {node: ^14.18.0 || >=16.0.0}

  tabster@8.5.6:
    resolution: {integrity: sha512-2vfrRGrx8O9BjdrtSlVA5fvpmbq5HQBRN13XFRg6LAvZ1Fr3QdBnswgT4YgFS5Bhoo5nxwgjRaRueI2Us/dv7g==}

  tailwindcss@3.4.17:
    resolution: {integrity: sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  tapable@2.2.2:
    resolution: {integrity: sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==}
    engines: {node: '>=6'}

  terser-webpack-plugin@5.3.14:
    resolution: {integrity: sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true

  terser@5.42.0:
    resolution: {integrity: sha512-UYCvU9YQW2f/Vwl+P0GfhxJxbUGLwd+5QrrGgLajzWAtC/23AX0vcise32kkP7Eu0Wu9VlzzHAXkLObgjQfFlQ==}
    engines: {node: '>=10'}
    hasBin: true

  textarea-caret-ts@4.1.1:
    resolution: {integrity: sha512-kEL3aQ99r8jJR2RfB6g74LEzrt9NTXkyjPvvP3vzhNkQ+zAaXitKCq8BUEueepULWCCxtpRaRjtnYHaR45FDMg==}

  thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}

  tlds@1.259.0:
    resolution: {integrity: sha512-AldGGlDP0PNgwppe2quAvuBl18UcjuNtOnDuUkqhd6ipPqrYYBt3aTxK1QTsBVknk97lS2JcafWMghjGWFtunw==}
    hasBin: true

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toggle-selection@1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}

  tree-kill@1.2.2:
    resolution: {integrity: sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==}
    hasBin: true

  ts-api-utils@2.1.0:
    resolution: {integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  ts-interface-checker@0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}

  ts-loader@9.5.2:
    resolution: {integrity: sha512-Qo4piXvOTWcMGIgRiuFa6nHNm+54HbYaZCKqc9eeZCLRy3XqafQgwX2F7mofrbJG3g7EEb+lkiR+z2Lic2s3Zw==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      typescript: '*'
      webpack: ^5.0.0

  tsconfig-paths@3.15.0:
    resolution: {integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==}

  tslib@2.4.0:
    resolution: {integrity: sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  tsx@4.20.3:
    resolution: {integrity: sha512-qjbnuR9Tr+FJOMBqJCW5ehvIo/buZq7vH7qD7JziU98h6l3qGy0a/yPFjwO+y0/T7GFpNgNAvEcPPVfyT8rrPQ==}
    engines: {node: '>=18.0.0'}
    hasBin: true

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  typed-array-buffer@1.0.3:
    resolution: {integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.3:
    resolution: {integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.7:
    resolution: {integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==}
    engines: {node: '>= 0.4'}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  uc.micro@1.0.6:
    resolution: {integrity: sha512-8Y75pvTYkLJW2hWQHXxoqRgV7qb9B+9vFEtidML+7koHUFapnVJAZ6cKs+Qjz5Aw3aZWHMC6u0wJE3At+nSGwA==}

  unbox-primitive@1.1.0:
    resolution: {integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==}
    engines: {node: '>= 0.4'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  use-callback-ref@1.3.3:
    resolution: {integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-debounce@10.0.5:
    resolution: {integrity: sha512-Q76E3lnIV+4YT9AHcrHEHYmAd9LKwUAbPXDm7FlqVGDHiSOhX3RDjT8dm0AxbJup6WgOb1YEcKyCr11kBJR5KQ==}
    engines: {node: '>= 16.0.0'}
    peerDependencies:
      react: '*'

  use-sidecar@1.1.3:
    resolution: {integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sync-external-store@1.5.0:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  watchpack@2.4.4:
    resolution: {integrity: sha512-c5EGNOiyxxV5qmTtAB7rbiXxi1ooX1pQKMLX/MIabJjRA0SJBQOjKF+KSVfHkr9U1cADPon0mRiVe/riyaiDUA==}
    engines: {node: '>=10.13.0'}

  webpack-sources@3.3.2:
    resolution: {integrity: sha512-ykKKus8lqlgXX/1WjudpIEjqsafjOTcOJqxnAbMLAu/KCsDCJ6GBtvscewvTkrn24HsnvFwrSCbenFrhtcCsAA==}
    engines: {node: '>=10.13.0'}

  webpack@5.99.9:
    resolution: {integrity: sha512-brOPwM3JnmOa+7kd3NsmOUOwbDAj8FT9xDsG3IW0MgbN9yZV7Oi/s/+MNQ/EcSMqw7qfoRyXPoeEWT8zLVdVGg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true

  which-boxed-primitive@1.1.1:
    resolution: {integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==}
    engines: {node: '>= 0.4'}

  which-builtin-type@1.2.1:
    resolution: {integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.19:
    resolution: {integrity: sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==}
    engines: {node: '>= 0.4'}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  ws@7.5.10:
    resolution: {integrity: sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  yaml@2.8.0:
    resolution: {integrity: sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==}
    engines: {node: '>= 14.6'}
    hasBin: true

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  zustand@5.0.5:
    resolution: {integrity: sha512-mILtRfKW9xM47hqxGIxCv12gXusoY/xTSHBYApXozR0HmQv299whhBeeAcRy+KrPPybzosvJBCOmVjq6x12fCg==}
    engines: {node: '>=12.20.0'}
    peerDependencies:
      '@types/react': '>=18.0.0'
      immer: '>=9.0.6'
      react: '>=18.0.0'
      use-sync-external-store: '>=1.2.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
      use-sync-external-store:
        optional: true

snapshots:

  '@alloc/quick-lru@5.2.0': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@azure-rest/core-client@2.4.0':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-auth': 1.9.0
      '@azure/core-rest-pipeline': 1.21.0
      '@azure/core-tracing': 1.2.0
      '@typespec/ts-http-runtime': 0.2.3
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/abort-controller@1.1.0':
    dependencies:
      tslib: 2.8.1

  '@azure/abort-controller@2.1.2':
    dependencies:
      tslib: 2.8.1

  '@azure/communication-calling-effects@1.1.4':
    dependencies:
      '@azure/logger': 1.2.0
      events: 3.3.0
    transitivePeerDependencies:
      - supports-color

  '@azure/communication-calling@1.35.1':
    dependencies:
      '@azure/communication-common': 2.4.0
      '@azure/logger': 1.2.0
    transitivePeerDependencies:
      - supports-color

  '@azure/communication-chat@1.5.4':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/communication-common': 2.4.0
      '@azure/communication-signaling': 1.0.0-beta.29
      '@azure/core-auth': 1.9.0
      '@azure/core-client': 1.9.4
      '@azure/core-paging': 1.6.2
      '@azure/core-rest-pipeline': 1.21.0
      '@azure/core-tracing': 1.2.0
      '@azure/logger': 1.2.0
      events: 3.3.0
      tslib: 2.8.1
      uuid: 8.3.2
    transitivePeerDependencies:
      - supports-color

  '@azure/communication-common@2.4.0':
    dependencies:
      '@azure-rest/core-client': 2.4.0
      '@azure/abort-controller': 2.1.2
      '@azure/core-auth': 1.9.0
      '@azure/core-rest-pipeline': 1.21.0
      '@azure/core-tracing': 1.2.0
      '@azure/core-util': 1.12.0
      events: 3.3.0
      jwt-decode: 4.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/communication-identity@1.3.1':
    dependencies:
      '@azure/abort-controller': 1.1.0
      '@azure/communication-common': 2.4.0
      '@azure/core-auth': 1.9.0
      '@azure/core-client': 1.9.4
      '@azure/core-lro': 2.7.2
      '@azure/core-paging': 1.6.2
      '@azure/core-rest-pipeline': 1.21.0
      '@azure/core-tracing': 1.2.0
      '@azure/logger': 1.2.0
      events: 3.3.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/communication-react@1.28.0(@azure/communication-calling-effects@1.1.4)(@azure/communication-calling@1.35.1)(@azure/communication-chat@1.5.4)(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@azure/communication-calling': 1.35.1
      '@azure/communication-calling-effects': 1.1.4
      '@azure/communication-chat': 1.5.4
      '@azure/communication-common': 2.4.0
      '@azure/core-paging': 1.6.2
      '@azure/logger': 1.2.0
      '@fluentui-contrib/react-chat': 0.1.11(@fluentui/react-components@9.62.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2))(@fluentui/react-icons@2.0.303(react@18.3.1))(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react': 8.123.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-components': 9.62.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-file-type-icons': 8.13.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-hooks': 8.8.19(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-window-provider': 2.2.30(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@types/events': 3.0.3
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      copy-to-clipboard: 3.3.3
      dompurify: 3.2.6
      events: 3.3.0
      html-react-parser: 5.2.5(@types/react@19.1.5)(react@18.3.1)
      immer: 10.1.1
      memoize-one: 5.2.1
      nanoid: 3.3.8
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-linkify: 1.0.0-alpha
      react-use-draggable-scroll: 0.4.7(react@18.3.1)
      reselect: 4.1.8
      roosterjs-content-model-api: 9.29.3
      roosterjs-content-model-core: 9.29.3
      roosterjs-content-model-dom: 9.29.3
      roosterjs-content-model-plugins: 9.29.3
      roosterjs-content-model-types: 9.29.3
      textarea-caret-ts: 4.1.1
      use-debounce: 10.0.5(react@18.3.1)
      uuid: 9.0.1
    transitivePeerDependencies:
      - scheduler
      - supports-color

  '@azure/communication-signaling@1.0.0-beta.29':
    dependencies:
      '@azure/abort-controller': 1.1.0
      '@azure/core-auth': 1.9.0
      '@azure/core-client': 1.9.4
      '@azure/core-rest-pipeline': 1.21.0
      '@azure/core-tracing': 1.2.0
      '@azure/core-util': 1.12.0
      '@azure/logger': 1.2.0
      events: 3.3.0
      tslib: 2.8.1
      uuid: 8.3.2
    transitivePeerDependencies:
      - supports-color

  '@azure/core-auth@1.9.0':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-util': 1.12.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/core-client@1.9.4':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-auth': 1.9.0
      '@azure/core-rest-pipeline': 1.21.0
      '@azure/core-tracing': 1.2.0
      '@azure/core-util': 1.12.0
      '@azure/logger': 1.2.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/core-lro@2.7.2':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-util': 1.12.0
      '@azure/logger': 1.2.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/core-paging@1.6.2':
    dependencies:
      tslib: 2.8.1

  '@azure/core-rest-pipeline@1.21.0':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-auth': 1.9.0
      '@azure/core-tracing': 1.2.0
      '@azure/core-util': 1.12.0
      '@azure/logger': 1.2.0
      '@typespec/ts-http-runtime': 0.2.3
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/core-tracing@1.2.0':
    dependencies:
      tslib: 2.8.1

  '@azure/core-util@1.12.0':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@typespec/ts-http-runtime': 0.2.3
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/logger@1.2.0':
    dependencies:
      '@typespec/ts-http-runtime': 0.2.3
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.27.5': {}

  '@babel/core@7.27.4':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.5':
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.27.5
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.0
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.27.6':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6

  '@babel/parser@7.27.5':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/runtime@7.27.6': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6

  '@babel/traverse@7.27.4':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.6':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@babylonjs/core@6.49.0': {}

  '@babylonjs/gui@6.49.0(@babylonjs/core@6.49.0)':
    dependencies:
      '@babylonjs/core': 6.49.0

  '@chakra-ui/anatomy@2.3.6': {}

  '@chakra-ui/hooks@2.4.5(react@18.3.1)':
    dependencies:
      '@chakra-ui/utils': 2.2.5(react@18.3.1)
      '@zag-js/element-size': 0.31.1
      copy-to-clipboard: 3.3.3
      framesync: 6.1.2
      react: 18.3.1

  '@chakra-ui/icons@2.2.4(@chakra-ui/react@2.10.9(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react': 2.10.9(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/icons@2.2.4(@chakra-ui/react@2.10.9(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react': 2.10.9(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react@2.10.9(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/hooks': 2.4.5(react@18.3.1)
      '@chakra-ui/styled-system': 2.12.4(react@18.3.1)
      '@chakra-ui/theme': 3.4.9(@chakra-ui/styled-system@2.12.4(react@18.3.1))(react@18.3.1)
      '@chakra-ui/utils': 2.2.5(react@18.3.1)
      '@emotion/react': 11.14.0(@types/react@18.3.23)(react@18.3.1)
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1)
      '@popperjs/core': 2.11.8
      '@zag-js/focus-visible': 0.31.1
      aria-hidden: 1.2.6
      framer-motion: 11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-fast-compare: 3.2.2
      react-focus-lock: 2.13.6(@types/react@18.3.23)(react@18.3.1)
      react-remove-scroll: 2.7.1(@types/react@18.3.23)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@chakra-ui/react@2.10.9(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/hooks': 2.4.5(react@18.3.1)
      '@chakra-ui/styled-system': 2.12.4(react@18.3.1)
      '@chakra-ui/theme': 3.4.9(@chakra-ui/styled-system@2.12.4(react@18.3.1))(react@18.3.1)
      '@chakra-ui/utils': 2.2.5(react@18.3.1)
      '@emotion/react': 11.14.0(@types/react@19.1.5)(react@18.3.1)
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(react@18.3.1)
      '@popperjs/core': 2.11.8
      '@zag-js/focus-visible': 0.31.1
      aria-hidden: 1.2.6
      framer-motion: 11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-fast-compare: 3.2.2
      react-focus-lock: 2.13.6(@types/react@19.1.5)(react@18.3.1)
      react-remove-scroll: 2.7.1(@types/react@19.1.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@chakra-ui/styled-system@2.12.4(react@18.3.1)':
    dependencies:
      '@chakra-ui/utils': 2.2.5(react@18.3.1)
      csstype: 3.1.3
    transitivePeerDependencies:
      - react

  '@chakra-ui/theme-tools@2.2.9(@chakra-ui/styled-system@2.12.4(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/anatomy': 2.3.6
      '@chakra-ui/styled-system': 2.12.4(react@18.3.1)
      '@chakra-ui/utils': 2.2.5(react@18.3.1)
      color2k: 2.0.3
    transitivePeerDependencies:
      - react

  '@chakra-ui/theme@3.4.9(@chakra-ui/styled-system@2.12.4(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/anatomy': 2.3.6
      '@chakra-ui/styled-system': 2.12.4(react@18.3.1)
      '@chakra-ui/theme-tools': 2.2.9(@chakra-ui/styled-system@2.12.4(react@18.3.1))(react@18.3.1)
      '@chakra-ui/utils': 2.2.5(react@18.3.1)
    transitivePeerDependencies:
      - react

  '@chakra-ui/utils@2.2.5(react@18.3.1)':
    dependencies:
      '@types/lodash.mergewith': 4.6.9
      lodash.mergewith: 4.6.2
      react: 18.3.1

  '@ctrl/tinycolor@3.6.1': {}

  '@emotion/babel-plugin@11.13.5':
    dependencies:
      '@babel/helper-module-imports': 7.27.1
      '@babel/runtime': 7.27.6
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/serialize': 1.3.3
      babel-plugin-macros: 3.1.0
      convert-source-map: 1.9.0
      escape-string-regexp: 4.0.0
      find-root: 1.1.0
      source-map: 0.5.7
      stylis: 4.2.0
    transitivePeerDependencies:
      - supports-color

  '@emotion/cache@11.14.0':
    dependencies:
      '@emotion/memoize': 0.9.0
      '@emotion/sheet': 1.4.0
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      stylis: 4.2.0

  '@emotion/hash@0.9.2': {}

  '@emotion/is-prop-valid@1.3.1':
    dependencies:
      '@emotion/memoize': 0.9.0

  '@emotion/memoize@0.9.0': {}

  '@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/babel-plugin': 11.13.5
      '@emotion/cache': 11.14.0
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@18.3.1)
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      hoist-non-react-statics: 3.3.2
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.23
    transitivePeerDependencies:
      - supports-color

  '@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/babel-plugin': 11.13.5
      '@emotion/cache': 11.14.0
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@18.3.1)
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      hoist-non-react-statics: 3.3.2
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.5
    transitivePeerDependencies:
      - supports-color

  '@emotion/serialize@1.3.3':
    dependencies:
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/unitless': 0.10.0
      '@emotion/utils': 1.4.2
      csstype: 3.1.3

  '@emotion/sheet@1.4.0': {}

  '@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/babel-plugin': 11.13.5
      '@emotion/is-prop-valid': 1.3.1
      '@emotion/react': 11.14.0(@types/react@18.3.23)(react@18.3.1)
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@18.3.1)
      '@emotion/utils': 1.4.2
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.23
    transitivePeerDependencies:
      - supports-color

  '@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@18.3.1))(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/babel-plugin': 11.13.5
      '@emotion/is-prop-valid': 1.3.1
      '@emotion/react': 11.14.0(@types/react@19.1.5)(react@18.3.1)
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@18.3.1)
      '@emotion/utils': 1.4.2
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.5
    transitivePeerDependencies:
      - supports-color

  '@emotion/unitless@0.10.0': {}

  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@emotion/utils@1.4.2': {}

  '@emotion/weak-memoize@0.4.0': {}

  '@esbuild/aix-ppc64@0.25.5':
    optional: true

  '@esbuild/android-arm64@0.25.5':
    optional: true

  '@esbuild/android-arm@0.25.5':
    optional: true

  '@esbuild/android-x64@0.25.5':
    optional: true

  '@esbuild/darwin-arm64@0.25.5':
    optional: true

  '@esbuild/darwin-x64@0.25.5':
    optional: true

  '@esbuild/freebsd-arm64@0.25.5':
    optional: true

  '@esbuild/freebsd-x64@0.25.5':
    optional: true

  '@esbuild/linux-arm64@0.25.5':
    optional: true

  '@esbuild/linux-arm@0.25.5':
    optional: true

  '@esbuild/linux-ia32@0.25.5':
    optional: true

  '@esbuild/linux-loong64@0.25.5':
    optional: true

  '@esbuild/linux-mips64el@0.25.5':
    optional: true

  '@esbuild/linux-ppc64@0.25.5':
    optional: true

  '@esbuild/linux-riscv64@0.25.5':
    optional: true

  '@esbuild/linux-s390x@0.25.5':
    optional: true

  '@esbuild/linux-x64@0.25.5':
    optional: true

  '@esbuild/netbsd-arm64@0.25.5':
    optional: true

  '@esbuild/netbsd-x64@0.25.5':
    optional: true

  '@esbuild/openbsd-arm64@0.25.5':
    optional: true

  '@esbuild/openbsd-x64@0.25.5':
    optional: true

  '@esbuild/sunos-x64@0.25.5':
    optional: true

  '@esbuild/win32-arm64@0.25.5':
    optional: true

  '@esbuild/win32-ia32@0.25.5':
    optional: true

  '@esbuild/win32-x64@0.25.5':
    optional: true

  '@eslint-community/eslint-utils@4.7.0(eslint@9.29.0(jiti@1.21.7))':
    dependencies:
      eslint: 9.29.0(jiti@1.21.7)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.20.1':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.2.3': {}

  '@eslint/core@0.14.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/core@0.15.1':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 10.4.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.29.0': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.3.3':
    dependencies:
      '@eslint/core': 0.15.1
      levn: 0.4.1

  '@floating-ui/core@1.7.1':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/devtools@0.2.1(@floating-ui/dom@1.7.1)':
    dependencies:
      '@floating-ui/dom': 1.7.1

  '@floating-ui/dom@1.7.1':
    dependencies:
      '@floating-ui/core': 1.7.1
      '@floating-ui/utils': 0.2.9

  '@floating-ui/utils@0.2.9': {}

  '@fluentui-contrib/react-chat@0.1.11(@fluentui/react-components@9.62.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2))(@fluentui/react-icons@2.0.303(react@18.3.1))(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-components': 9.62.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/date-time-utilities@8.6.10':
    dependencies:
      '@fluentui/set-version': 8.2.24
      tslib: 2.8.1

  '@fluentui/dom-utilities@2.3.10':
    dependencies:
      '@fluentui/set-version': 8.2.24
      tslib: 2.8.1

  '@fluentui/font-icons-mdl2@8.5.62(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/set-version': 8.2.24
      '@fluentui/style-utilities': 8.12.2(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/utilities': 8.15.22(@types/react@19.1.5)(react@18.3.1)
      tslib: 2.8.1
    transitivePeerDependencies:
      - '@types/react'
      - react

  '@fluentui/foundation-legacy@8.4.28(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/merge-styles': 8.6.14
      '@fluentui/set-version': 8.2.24
      '@fluentui/style-utilities': 8.12.2(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/utilities': 8.15.22(@types/react@19.1.5)(react@18.3.1)
      '@types/react': 19.1.5
      react: 18.3.1
      tslib: 2.8.1

  '@fluentui/keyboard-key@0.4.23':
    dependencies:
      tslib: 2.8.1

  '@fluentui/keyboard-keys@9.0.8':
    dependencies:
      '@swc/helpers': 0.5.17

  '@fluentui/merge-styles@8.6.14':
    dependencies:
      '@fluentui/set-version': 8.2.24
      tslib: 2.8.1

  '@fluentui/priority-overflow@9.1.15':
    dependencies:
      '@swc/helpers': 0.5.17

  '@fluentui/react-accordion@9.7.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion-components-preview': 0.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-alert@9.0.0-beta.124(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-aria@9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-avatar@9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-badge': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-tooltip': 9.7.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-badge@9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-breadcrumb@9.2.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-link': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-button@9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-card@9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-text': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-carousel@9.7.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-tooltip': 9.7.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      embla-carousel: 8.6.0
      embla-carousel-autoplay: 8.6.0(embla-carousel@8.6.0)
      embla-carousel-fade: 8.6.0(embla-carousel@8.6.0)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-checkbox@9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-color-picker@9.1.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@ctrl/tinycolor': 3.6.1
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-combobox@9.15.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-components@9.62.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-accordion': 9.7.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-alert': 9.0.0-beta.124(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-badge': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-breadcrumb': 9.2.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-card': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-carousel': 9.7.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-checkbox': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-color-picker': 9.1.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-combobox': 9.15.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-dialog': 9.13.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-divider': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-drawer': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-image': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-infobutton': 9.0.0-beta.102(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-infolabel': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-input': 9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-label': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-link': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-list': 9.2.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-menu': 9.17.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-message-bar': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-overflow': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-persona': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-progress': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-provider': 9.21.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-radio': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-rating': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-search': 9.2.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-select': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-skeleton': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-slider': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-spinbutton': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-spinner': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-swatch-picker': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-switch': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-table': 9.17.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tabs': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-tag-picker': 9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tags': 9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-teaching-popover': 9.5.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-text': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-textarea': 9.5.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-toast': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-toolbar': 9.5.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tooltip': 9.7.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-tree': 9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-virtualizer': 9.0.0-alpha.96(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-components@9.66.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-accordion': 9.7.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-alert': 9.0.0-beta.124(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-badge': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-breadcrumb': 9.2.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-card': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-carousel': 9.7.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-checkbox': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-color-picker': 9.1.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-combobox': 9.15.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-dialog': 9.13.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-divider': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-drawer': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-image': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-infobutton': 9.0.0-beta.102(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-infolabel': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-input': 9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-label': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-link': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-list': 9.2.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-menu': 9.17.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-message-bar': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-nav': 9.1.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-overflow': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-persona': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-progress': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-provider': 9.21.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-radio': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-rating': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-search': 9.2.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-select': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-skeleton': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-slider': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-spinbutton': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-spinner': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-swatch-picker': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-switch': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-table': 9.17.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tabs': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-tag-picker': 9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tags': 9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-teaching-popover': 9.5.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-text': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-textarea': 9.5.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-toast': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-toolbar': 9.5.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tooltip': 9.7.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-tree': 9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-virtualizer': 9.0.0-alpha.98(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-context-selector@9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      scheduler: 0.23.2

  '@fluentui/react-dialog@9.13.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion-components-preview': 0.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-divider@9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-drawer@9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-dialog': 9.13.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-field@9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-file-type-icons@8.13.1(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/set-version': 8.2.24
      '@fluentui/style-utilities': 8.12.2(@types/react@19.1.5)(react@18.3.1)
      '@types/react': 19.1.5
      react: 18.3.1
      tslib: 2.8.1

  '@fluentui/react-focus@8.9.25(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-key': 0.4.23
      '@fluentui/merge-styles': 8.6.14
      '@fluentui/set-version': 8.2.24
      '@fluentui/style-utilities': 8.12.2(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/utilities': 8.15.22(@types/react@19.1.5)(react@18.3.1)
      '@types/react': 19.1.5
      react: 18.3.1
      tslib: 2.8.1

  '@fluentui/react-hooks@8.8.19(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/react-window-provider': 2.2.30(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/set-version': 8.2.24
      '@fluentui/utilities': 8.15.22(@types/react@19.1.5)(react@18.3.1)
      '@types/react': 19.1.5
      react: 18.3.1
      tslib: 2.8.1

  '@fluentui/react-icons@2.0.303(react@18.3.1)':
    dependencies:
      '@griffel/react': 1.5.30(react@18.3.1)
      react: 18.3.1
      tslib: 2.8.1

  '@fluentui/react-image@9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-infobutton@9.0.0-beta.102(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-infolabel@9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-input@9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-jsx-runtime@9.1.0(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      react: 18.3.1
      react-is: 17.0.2

  '@fluentui/react-label@9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-link@9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-list@9.2.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-checkbox': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-menu@9.17.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-message-bar@9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-link': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-transition-group: 4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)

  '@fluentui/react-motion-components-preview@0.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-motion': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-motion@9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-nav@9.1.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-divider': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-drawer': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-tooltip': 9.7.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-overflow@9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/priority-overflow': 9.1.15
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-persona@9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-badge': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-popover@9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-portal-compat-context@9.0.13(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      react: 18.3.1

  '@fluentui/react-portal@9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-positioning@9.18.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/devtools': 0.2.1(@floating-ui/dom@1.7.1)
      '@floating-ui/dom': 1.7.1
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      use-sync-external-store: 1.5.0(react@18.3.1)

  '@fluentui/react-progress@9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-provider@9.21.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/core': 1.19.2
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-radio@9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-rating@9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-search@9.2.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-input': 9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-select@9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-shared-contexts@9.23.1(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/react-theme': 9.1.24
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      react: 18.3.1

  '@fluentui/react-skeleton@9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-slider@9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-spinbutton@9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-spinner@9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-swatch-picker@9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-switch@9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-label': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-table@9.17.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-checkbox': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-radio': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tabs@9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tabster@9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      keyborg: 2.6.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      tabster: 8.5.6

  '@fluentui/react-tag-picker@9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-combobox': 9.15.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-tags': 9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tags@9.6.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-teaching-popover@9.5.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-popover': 9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      use-sync-external-store: 1.5.0(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-text@9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-textarea@9.5.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-field': 9.3.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-theme@9.1.24':
    dependencies:
      '@fluentui/tokens': 1.0.0-alpha.21
      '@swc/helpers': 0.5.17

  '@fluentui/react-toast@9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion-components-preview': 0.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-toolbar@9.5.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-divider': 9.3.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-radio': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tooltip@9.7.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-portal': 9.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-positioning': 9.18.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-tree@9.11.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-avatar': 9.8.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-button': 9.5.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-checkbox': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-context-selector': 9.2.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-icons': 2.0.303(react@18.3.1)
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-motion': 9.8.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-motion-components-preview': 0.6.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-radio': 9.4.1(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(scheduler@0.23.2)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-tabster': 9.25.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-utilities@9.21.0(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      react: 18.3.1

  '@fluentui/react-virtualizer@9.0.0-alpha.96(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-virtualizer@9.0.0-alpha.98(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.0(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-utilities': 9.21.0(@types/react@19.1.5)(react@18.3.1)
      '@griffel/react': 1.5.30(react@18.3.1)
      '@swc/helpers': 0.5.17
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@fluentui/react-window-provider@2.2.30(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/set-version': 8.2.24
      '@types/react': 19.1.5
      react: 18.3.1
      tslib: 2.8.1

  '@fluentui/react@8.123.0(@types/react-dom@19.1.6(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@fluentui/date-time-utilities': 8.6.10
      '@fluentui/font-icons-mdl2': 8.5.62(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/foundation-legacy': 8.4.28(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/merge-styles': 8.6.14
      '@fluentui/react-focus': 8.9.25(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-hooks': 8.8.19(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-portal-compat-context': 9.0.13(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/react-window-provider': 2.2.30(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/set-version': 8.2.24
      '@fluentui/style-utilities': 8.12.2(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/theme': 2.6.67(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/utilities': 8.15.22(@types/react@19.1.5)(react@18.3.1)
      '@microsoft/load-themed-styles': 1.10.295
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.6(@types/react@19.1.5)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      tslib: 2.8.1

  '@fluentui/set-version@8.2.24':
    dependencies:
      tslib: 2.8.1

  '@fluentui/style-utilities@8.12.2(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/merge-styles': 8.6.14
      '@fluentui/set-version': 8.2.24
      '@fluentui/theme': 2.6.67(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/utilities': 8.15.22(@types/react@19.1.5)(react@18.3.1)
      '@microsoft/load-themed-styles': 1.10.295
      tslib: 2.8.1
    transitivePeerDependencies:
      - '@types/react'
      - react

  '@fluentui/theme@2.6.67(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/merge-styles': 8.6.14
      '@fluentui/set-version': 8.2.24
      '@fluentui/utilities': 8.15.22(@types/react@19.1.5)(react@18.3.1)
      '@types/react': 19.1.5
      react: 18.3.1
      tslib: 2.8.1

  '@fluentui/tokens@1.0.0-alpha.21':
    dependencies:
      '@swc/helpers': 0.5.17

  '@fluentui/utilities@8.15.22(@types/react@19.1.5)(react@18.3.1)':
    dependencies:
      '@fluentui/dom-utilities': 2.3.10
      '@fluentui/merge-styles': 8.6.14
      '@fluentui/react-window-provider': 2.2.30(@types/react@19.1.5)(react@18.3.1)
      '@fluentui/set-version': 8.2.24
      '@types/react': 19.1.5
      react: 18.3.1
      tslib: 2.8.1

  '@griffel/core@1.19.2':
    dependencies:
      '@emotion/hash': 0.9.2
      '@griffel/style-types': 1.3.0
      csstype: 3.1.3
      rtl-css-js: 1.16.1
      stylis: 4.3.6
      tslib: 2.8.1

  '@griffel/react@1.5.30(react@18.3.1)':
    dependencies:
      '@griffel/core': 1.19.2
      react: 18.3.1
      tslib: 2.8.1

  '@griffel/style-types@1.3.0':
    dependencies:
      csstype: 3.1.3

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.3': {}

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/source-map@0.3.6':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@microsoft/load-themed-styles@1.10.295': {}

  '@next/env@14.1.0': {}

  '@next/eslint-plugin-next@15.3.4':
    dependencies:
      fast-glob: 3.3.1

  '@next/swc-darwin-arm64@14.1.0':
    optional: true

  '@next/swc-darwin-x64@14.1.0':
    optional: true

  '@next/swc-linux-arm64-gnu@14.1.0':
    optional: true

  '@next/swc-linux-arm64-musl@14.1.0':
    optional: true

  '@next/swc-linux-x64-gnu@14.1.0':
    optional: true

  '@next/swc-linux-x64-musl@14.1.0':
    optional: true

  '@next/swc-win32-arm64-msvc@14.1.0':
    optional: true

  '@next/swc-win32-ia32-msvc@14.1.0':
    optional: true

  '@next/swc-win32-x64-msvc@14.1.0':
    optional: true

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@opentelemetry/api@1.9.0':
    optional: true

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@pkgr/core@0.2.7': {}

  '@popperjs/core@2.11.8': {}

  '@rollup/rollup-linux-x64-gnu@4.40.0':
    optional: true

  '@rtsao/scc@1.1.0': {}

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1

  '@swc/helpers@0.5.2':
    dependencies:
      tslib: 2.8.1

  '@types/axios@0.9.36': {}

  '@types/eslint-scope@3.7.7':
    dependencies:
      '@types/eslint': 9.6.1
      '@types/estree': 1.0.8

  '@types/eslint@9.6.1':
    dependencies:
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15

  '@types/estree@1.0.8': {}

  '@types/events@3.0.3': {}

  '@types/json-schema@7.0.15': {}

  '@types/json5@0.0.29': {}

  '@types/lodash.mergewith@4.6.9':
    dependencies:
      '@types/lodash': 4.17.17

  '@types/lodash@4.17.17': {}

  '@types/node@20.5.7': {}

  '@types/parse-json@4.0.2': {}

  '@types/prop-types@15.7.15': {}

  '@types/react-dom@19.1.6(@types/react@19.1.5)':
    dependencies:
      '@types/react': 19.1.5

  '@types/react@18.3.23':
    dependencies:
      '@types/prop-types': 15.7.15
      csstype: 3.1.3

  '@types/react@19.1.5':
    dependencies:
      csstype: 3.1.3

  '@types/trusted-types@2.0.7':
    optional: true

  '@types/webrtc@0.0.37': {}

  '@typescript-eslint/eslint-plugin@8.35.0(@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)
      '@typescript-eslint/scope-manager': 8.35.0
      '@typescript-eslint/type-utils': 8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.35.0
      eslint: 9.29.0(jiti@1.21.7)
      graphemer: 1.4.0
      ignore: 7.0.5
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.35.0
      '@typescript-eslint/types': 8.35.0
      '@typescript-eslint/typescript-estree': 8.35.0(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.35.0
      debug: 4.4.1
      eslint: 9.29.0(jiti@1.21.7)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/project-service@8.35.0(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/tsconfig-utils': 8.35.0(typescript@5.8.3)
      '@typescript-eslint/types': 8.35.0
      debug: 4.4.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.35.0':
    dependencies:
      '@typescript-eslint/types': 8.35.0
      '@typescript-eslint/visitor-keys': 8.35.0

  '@typescript-eslint/tsconfig-utils@8.35.0(typescript@5.8.3)':
    dependencies:
      typescript: 5.8.3

  '@typescript-eslint/type-utils@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.35.0(typescript@5.8.3)
      '@typescript-eslint/utils': 8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)
      debug: 4.4.1
      eslint: 9.29.0(jiti@1.21.7)
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.35.0': {}

  '@typescript-eslint/typescript-estree@8.35.0(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/project-service': 8.35.0(typescript@5.8.3)
      '@typescript-eslint/tsconfig-utils': 8.35.0(typescript@5.8.3)
      '@typescript-eslint/types': 8.35.0
      '@typescript-eslint/visitor-keys': 8.35.0
      debug: 4.4.1
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.29.0(jiti@1.21.7))
      '@typescript-eslint/scope-manager': 8.35.0
      '@typescript-eslint/types': 8.35.0
      '@typescript-eslint/typescript-estree': 8.35.0(typescript@5.8.3)
      eslint: 9.29.0(jiti@1.21.7)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.35.0':
    dependencies:
      '@typescript-eslint/types': 8.35.0
      eslint-visitor-keys: 4.2.1

  '@typespec/ts-http-runtime@0.2.3':
    dependencies:
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@webassemblyjs/ast@1.14.1':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2

  '@webassemblyjs/floating-point-hex-parser@1.13.2': {}

  '@webassemblyjs/helper-api-error@1.13.2': {}

  '@webassemblyjs/helper-buffer@1.14.1': {}

  '@webassemblyjs/helper-numbers@1.13.2':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.13.2': {}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1

  '@webassemblyjs/ieee754@1.13.2':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.13.2':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.13.2': {}

  '@webassemblyjs/wasm-edit@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1

  '@webassemblyjs/wasm-gen@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wasm-opt@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1

  '@webassemblyjs/wasm-parser@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wast-printer@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  '@zag-js/dom-query@0.31.1': {}

  '@zag-js/element-size@0.31.1': {}

  '@zag-js/focus-visible@0.31.1':
    dependencies:
      '@zag-js/dom-query': 0.31.1

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn@8.15.0: {}

  agent-base@5.1.1: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  agent-base@7.1.3: {}

  ajv-formats@2.1.1(ajv@8.17.1):
    optionalDependencies:
      ajv: 8.17.1

  ajv-keywords@5.1.0(ajv@8.17.1):
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@5.0.2: {}

  argparse@2.0.1: {}

  aria-hidden@1.2.6:
    dependencies:
      tslib: 2.8.1

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5

  array-includes@3.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1
      math-intrinsics: 1.1.0

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.findlastindex@1.2.6:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  async-function@1.0.0: {}

  asynckit@0.4.0: {}

  autoprefixer@10.4.19(postcss@8.4.38):
    dependencies:
      browserslist: 4.25.0
      caniuse-lite: 1.0.30001723
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  axios@1.10.0:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.3
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-loader@10.0.0(@babel/core@7.27.4)(webpack@5.99.9):
    dependencies:
      '@babel/core': 7.27.4
      find-up: 5.0.0
      webpack: 5.99.9

  babel-plugin-macros@3.1.0:
    dependencies:
      '@babel/runtime': 7.27.6
      cosmiconfig: 7.1.0
      resolve: 1.22.10

  balanced-match@1.0.2: {}

  bent@7.3.12:
    dependencies:
      bytesish: 0.4.4
      caseless: 0.12.0
      is-stream: 2.0.1

  binary-extensions@2.3.0: {}

  brace-expansion@1.1.12:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.25.0:
    dependencies:
      caniuse-lite: 1.0.30001723
      electron-to-chromium: 1.5.167
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.0)

  buffer-from@1.1.2: {}

  busboy@1.6.0:
    dependencies:
      streamsearch: 1.1.0

  bytesish@0.4.4: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  camelcase-css@2.0.1: {}

  caniuse-lite@1.0.30001723: {}

  caseless@0.12.0: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chrome-trace-event@1.0.4: {}

  client-only@0.0.1: {}

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  color2k@2.0.3: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.20.3: {}

  commander@4.1.1: {}

  concat-map@0.0.1: {}

  concurrently@8.2.2:
    dependencies:
      chalk: 4.1.2
      date-fns: 2.30.0
      lodash: 4.17.21
      rxjs: 7.8.2
      shell-quote: 1.8.3
      spawn-command: 0.0.2
      supports-color: 8.1.1
      tree-kill: 1.2.2
      yargs: 17.7.2

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  copy-to-clipboard@3.3.3:
    dependencies:
      toggle-selection: 1.0.6

  cosmiconfig@7.1.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.1
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  cssesc@3.0.0: {}

  csstype@3.1.3: {}

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  date-fns@2.30.0:
    dependencies:
      '@babel/runtime': 7.27.6

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  deep-is@0.1.4: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  detect-node-es@1.1.0: {}

  didyoumean@1.2.2: {}

  dlv@1.1.3: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.27.6
      csstype: 3.1.3

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domelementtype@2.3.0: {}

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  dompurify@3.2.6:
    optionalDependencies:
      '@types/trusted-types': 2.0.7

  domutils@3.2.2:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dotenv@16.5.0: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  eastasianwidth@0.2.0: {}

  electron-to-chromium@1.5.167: {}

  embla-carousel-autoplay@8.6.0(embla-carousel@8.6.0):
    dependencies:
      embla-carousel: 8.6.0

  embla-carousel-fade@8.6.0(embla-carousel@8.6.0):
    dependencies:
      embla-carousel: 8.6.0

  embla-carousel@8.6.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  entities@4.5.0: {}

  entities@6.0.1: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-abstract@1.24.0:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-negative-zero: 2.0.3
      is-regex: 1.2.1
      is-set: 2.0.3
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      stop-iteration-iterator: 1.1.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3

  es-module-lexer@1.7.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.1.0:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  esbuild@0.25.5:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.5
      '@esbuild/android-arm': 0.25.5
      '@esbuild/android-arm64': 0.25.5
      '@esbuild/android-x64': 0.25.5
      '@esbuild/darwin-arm64': 0.25.5
      '@esbuild/darwin-x64': 0.25.5
      '@esbuild/freebsd-arm64': 0.25.5
      '@esbuild/freebsd-x64': 0.25.5
      '@esbuild/linux-arm': 0.25.5
      '@esbuild/linux-arm64': 0.25.5
      '@esbuild/linux-ia32': 0.25.5
      '@esbuild/linux-loong64': 0.25.5
      '@esbuild/linux-mips64el': 0.25.5
      '@esbuild/linux-ppc64': 0.25.5
      '@esbuild/linux-riscv64': 0.25.5
      '@esbuild/linux-s390x': 0.25.5
      '@esbuild/linux-x64': 0.25.5
      '@esbuild/netbsd-arm64': 0.25.5
      '@esbuild/netbsd-x64': 0.25.5
      '@esbuild/openbsd-arm64': 0.25.5
      '@esbuild/openbsd-x64': 0.25.5
      '@esbuild/sunos-x64': 0.25.5
      '@esbuild/win32-arm64': 0.25.5
      '@esbuild/win32-ia32': 0.25.5
      '@esbuild/win32-x64': 0.25.5

  escalade@3.2.0: {}

  escape-string-regexp@4.0.0: {}

  eslint-config-prettier@10.1.5(eslint@9.29.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.29.0(jiti@1.21.7)

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.16.1
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint@9.29.0(jiti@1.21.7)):
    dependencies:
      debug: 3.2.7
    optionalDependencies:
      '@typescript-eslint/parser': 8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)
      eslint: 9.29.0(jiti@1.21.7)
      eslint-import-resolver-node: 0.3.9
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-es@3.0.1(eslint@9.29.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.29.0(jiti@1.21.7)
      eslint-utils: 2.1.0
      regexpp: 3.2.0

  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.29.0(jiti@1.21.7)):
    dependencies:
      '@rtsao/scc': 1.1.0
      array-includes: 3.1.9
      array.prototype.findlastindex: 1.2.6
      array.prototype.flat: 1.3.3
      array.prototype.flatmap: 1.3.3
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 9.29.0(jiti@1.21.7)
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.12.0(@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint@9.29.0(jiti@1.21.7))
      hasown: 2.0.2
      is-core-module: 2.16.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.1
      semver: 6.3.1
      string.prototype.trimend: 1.0.9
      tsconfig-paths: 3.15.0
    optionalDependencies:
      '@typescript-eslint/parser': 8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-node@11.1.0(eslint@9.29.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.29.0(jiti@1.21.7)
      eslint-plugin-es: 3.0.1(eslint@9.29.0(jiti@1.21.7))
      eslint-utils: 2.1.0
      ignore: 5.3.2
      minimatch: 3.1.2
      resolve: 1.22.10
      semver: 6.3.1

  eslint-plugin-prettier@5.5.1(@types/eslint@9.6.1)(eslint-config-prettier@10.1.5(eslint@9.29.0(jiti@1.21.7)))(eslint@9.29.0(jiti@1.21.7))(prettier@3.6.1):
    dependencies:
      eslint: 9.29.0(jiti@1.21.7)
      prettier: 3.6.1
      prettier-linter-helpers: 1.0.0
      synckit: 0.11.8
    optionalDependencies:
      '@types/eslint': 9.6.1
      eslint-config-prettier: 10.1.5(eslint@9.29.0(jiti@1.21.7))

  eslint-plugin-react-hooks@5.2.0(eslint@9.29.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.29.0(jiti@1.21.7)

  eslint-plugin-react@7.37.5(eslint@9.29.0(jiti@1.21.7)):
    dependencies:
      array-includes: 3.1.9
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 9.29.0(jiti@1.21.7)
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-plugin-unused-imports@4.1.4(@typescript-eslint/eslint-plugin@8.35.0(@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.29.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.29.0(jiti@1.21.7)
    optionalDependencies:
      '@typescript-eslint/eslint-plugin': 8.35.0(@typescript-eslint/parser@8.35.0(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.29.0(jiti@1.21.7))(typescript@5.8.3)

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@8.4.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-utils@2.1.0:
    dependencies:
      eslint-visitor-keys: 1.3.0

  eslint-visitor-keys@1.3.0: {}

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.1: {}

  eslint@9.29.0(jiti@1.21.7):
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.29.0(jiti@1.21.7))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.20.1
      '@eslint/config-helpers': 0.2.3
      '@eslint/core': 0.14.0
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.29.0
      '@eslint/plugin-kit': 0.3.3
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.3
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 1.21.7
    transitivePeerDependencies:
      - supports-color

  espree@10.4.0:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 4.2.1

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  events@3.3.0: {}

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-glob@3.3.1:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-root@1.1.0: {}

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flatted@3.3.3: {}

  focus-lock@1.3.6:
    dependencies:
      tslib: 2.8.1

  follow-redirects@1.15.9: {}

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  form-data@4.0.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35

  fraction.js@4.3.7: {}

  framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      motion-dom: 11.18.1
      motion-utils: 11.18.1
      tslib: 2.8.1
    optionalDependencies:
      '@emotion/is-prop-valid': 1.3.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  framer-motion@12.18.1(@emotion/is-prop-valid@1.3.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      motion-dom: 12.18.1
      motion-utils: 12.18.1
      tslib: 2.8.1
    optionalDependencies:
      '@emotion/is-prop-valid': 1.3.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  framesync@6.1.2:
    dependencies:
      tslib: 2.4.0

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-nonce@1.0.1: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  get-them-args@1.3.2: {}

  get-tsconfig@4.10.1:
    dependencies:
      resolve-pkg-maps: 1.0.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@10.3.10:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 2.3.6
      minimatch: 9.0.5
      minipass: 7.1.2
      path-scurry: 1.11.1

  globals@11.12.0: {}

  globals@14.0.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  has-bigints@1.1.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  html-dom-parser@5.1.1:
    dependencies:
      domhandler: 5.0.3
      htmlparser2: 10.0.0

  html-react-parser@5.2.5(@types/react@19.1.5)(react@18.3.1):
    dependencies:
      domhandler: 5.0.3
      html-dom-parser: 5.1.1
      react: 18.3.1
      react-property: 2.0.2
      style-to-js: 1.1.16
    optionalDependencies:
      '@types/react': 19.1.5

  htmlparser2@10.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2
      entities: 6.0.1

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@4.0.0:
    dependencies:
      agent-base: 5.1.1
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  ignore@5.3.2: {}

  ignore@7.0.5: {}

  immer@10.1.1: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  inline-style-parser@0.2.4: {}

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-arrayish@0.2.1: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-fullwidth-code-point@3.0.0: {}

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-map@2.0.3: {}

  is-negative-zero@2.0.3: {}

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.4

  is-stream@2.0.1: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.19

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  iterator.prototype@1.1.5:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2

  jackspeak@2.3.6:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 20.5.7
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jiti@1.21.7: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.9
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  jwt-decode@4.0.0: {}

  keyborg@2.6.0: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kill-port@2.0.1:
    dependencies:
      get-them-args: 1.3.2
      shell-exec: 1.0.2

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lilconfig@3.1.3: {}

  lines-and-columns@1.2.4: {}

  linkify-it@2.2.0:
    dependencies:
      uc.micro: 1.0.6

  loader-runner@4.3.0: {}

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.merge@4.6.2: {}

  lodash.mergewith@4.6.2: {}

  lodash@4.17.21: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  math-intrinsics@1.1.0: {}

  memoize-one@5.2.1: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  microsoft-cognitiveservices-speech-sdk@1.44.1:
    dependencies:
      '@types/webrtc': 0.0.37
      agent-base: 6.0.2
      bent: 7.3.12
      https-proxy-agent: 4.0.0
      uuid: 9.0.1
      ws: 7.5.10
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  motion-dom@11.18.1:
    dependencies:
      motion-utils: 11.18.1

  motion-dom@12.18.1:
    dependencies:
      motion-utils: 12.18.1

  motion-utils@11.18.1: {}

  motion-utils@12.18.1: {}

  ms@2.1.3: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.11: {}

  nanoid@3.3.8: {}

  natural-compare@1.4.0: {}

  neo-async@2.6.2: {}

  next@14.1.0(@babel/core@7.27.4)(@opentelemetry/api@1.9.0)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@next/env': 14.1.0
      '@swc/helpers': 0.5.2
      busboy: 1.6.0
      caniuse-lite: 1.0.30001723
      graceful-fs: 4.2.11
      postcss: 8.4.31
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      styled-jsx: 5.1.1(@babel/core@7.27.4)(react@18.3.1)
    optionalDependencies:
      '@next/swc-darwin-arm64': 14.1.0
      '@next/swc-darwin-x64': 14.1.0
      '@next/swc-linux-arm64-gnu': 14.1.0
      '@next/swc-linux-arm64-musl': 14.1.0
      '@next/swc-linux-x64-gnu': 14.1.0
      '@next/swc-linux-x64-musl': 14.1.0
      '@next/swc-win32-arm64-msvc': 14.1.0
      '@next/swc-win32-ia32-msvc': 14.1.0
      '@next/swc-win32-x64-msvc': 14.1.0
      '@opentelemetry/api': 1.9.0
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-macros

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.13.4: {}

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1

  object.groupby@1.0.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  path-exists@4.0.0: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-type@4.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  pify@2.3.0: {}

  pirates@4.0.7: {}

  possible-typed-array-names@1.1.0: {}

  postcss-import@15.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.10

  postcss-js@4.0.1(postcss@8.5.6):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.5.6

  postcss-load-config@4.0.2(postcss@8.5.6):
    dependencies:
      lilconfig: 3.1.3
      yaml: 2.8.0
    optionalDependencies:
      postcss: 8.5.6

  postcss-nested@6.2.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.4.31:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  postcss@8.4.38:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@3.6.1: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-from-env@1.1.0: {}

  punycode@2.3.1: {}

  queue-microtask@1.2.3: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  react-clientside-effect@1.2.8(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.27.6
      react: 18.3.1

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-fast-compare@3.2.2: {}

  react-focus-lock@2.13.6(@types/react@18.3.23)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.27.6
      focus-lock: 1.3.6
      prop-types: 15.8.1
      react: 18.3.1
      react-clientside-effect: 1.2.8(react@18.3.1)
      use-callback-ref: 1.3.3(@types/react@18.3.23)(react@18.3.1)
      use-sidecar: 1.1.3(@types/react@18.3.23)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.23

  react-focus-lock@2.13.6(@types/react@19.1.5)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.27.6
      focus-lock: 1.3.6
      prop-types: 15.8.1
      react: 18.3.1
      react-clientside-effect: 1.2.8(react@18.3.1)
      use-callback-ref: 1.3.3(@types/react@19.1.5)(react@18.3.1)
      use-sidecar: 1.1.3(@types/react@19.1.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.5

  react-icons@5.5.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  react-intersection-observer@9.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      react: 18.3.1
    optionalDependencies:
      react-dom: 18.3.1(react@18.3.1)

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-linkify@1.0.0-alpha:
    dependencies:
      linkify-it: 2.2.0
      tlds: 1.259.0

  react-property@2.0.2: {}

  react-remove-scroll-bar@2.3.8(@types/react@18.3.23)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-style-singleton: 2.2.3(@types/react@18.3.23)(react@18.3.1)
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 18.3.23

  react-remove-scroll-bar@2.3.8(@types/react@19.1.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-style-singleton: 2.2.3(@types/react@19.1.5)(react@18.3.1)
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.5

  react-remove-scroll@2.7.1(@types/react@18.3.23)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-remove-scroll-bar: 2.3.8(@types/react@18.3.23)(react@18.3.1)
      react-style-singleton: 2.2.3(@types/react@18.3.23)(react@18.3.1)
      tslib: 2.8.1
      use-callback-ref: 1.3.3(@types/react@18.3.23)(react@18.3.1)
      use-sidecar: 1.1.3(@types/react@18.3.23)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.23

  react-remove-scroll@2.7.1(@types/react@19.1.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-remove-scroll-bar: 2.3.8(@types/react@19.1.5)(react@18.3.1)
      react-style-singleton: 2.2.3(@types/react@19.1.5)(react@18.3.1)
      tslib: 2.8.1
      use-callback-ref: 1.3.3(@types/react@19.1.5)(react@18.3.1)
      use-sidecar: 1.1.3(@types/react@19.1.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.5

  react-style-singleton@2.2.3(@types/react@18.3.23)(react@18.3.1):
    dependencies:
      get-nonce: 1.0.1
      react: 18.3.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 18.3.23

  react-style-singleton@2.2.3(@types/react@19.1.5)(react@18.3.1):
    dependencies:
      get-nonce: 1.0.1
      react: 18.3.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.5

  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.27.6
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react-use-draggable-scroll@0.4.7(react@18.3.1):
    dependencies:
      react: 18.3.1

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  regexpp@3.2.0: {}

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  reselect@4.1.8: {}

  resolve-from@4.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  reusify@1.1.0: {}

  roosterjs-content-model-api@9.29.3:
    dependencies:
      roosterjs-content-model-dom: 9.29.3
      roosterjs-content-model-types: 9.29.3
      tslib: 2.8.1

  roosterjs-content-model-core@9.29.3:
    dependencies:
      roosterjs-content-model-dom: 9.29.3
      roosterjs-content-model-types: 9.29.3
      tslib: 2.8.1

  roosterjs-content-model-dom@9.29.3:
    dependencies:
      roosterjs-content-model-types: 9.29.3
      tslib: 2.8.1

  roosterjs-content-model-plugins@9.29.3:
    dependencies:
      roosterjs-content-model-api: 9.29.3
      roosterjs-content-model-core: 9.29.3
      roosterjs-content-model-dom: 9.29.3
      roosterjs-content-model-types: 9.29.3
      tslib: 2.8.1

  roosterjs-content-model-types@9.29.3: {}

  rtl-css-js@1.16.1:
    dependencies:
      '@babel/runtime': 7.27.6

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.2.1: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  schema-utils@4.3.2:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      ajv-keywords: 5.1.0(ajv@8.17.1)

  semver@6.3.1: {}

  semver@7.7.2: {}

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-exec@1.0.2: {}

  shell-quote@1.8.3: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@4.1.0: {}

  source-map-js@1.2.1: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.5.7: {}

  source-map@0.6.1: {}

  source-map@0.7.4: {}

  spawn-command@0.0.2: {}

  stop-iteration-iterator@1.1.0:
    dependencies:
      es-errors: 1.3.0
      internal-slot: 1.1.0

  streamsearch@1.1.0: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.24.0

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-bom@3.0.0: {}

  strip-json-comments@3.1.1: {}

  style-to-js@1.1.16:
    dependencies:
      style-to-object: 1.0.8

  style-to-object@1.0.8:
    dependencies:
      inline-style-parser: 0.2.4

  styled-jsx@5.1.1(@babel/core@7.27.4)(react@18.3.1):
    dependencies:
      client-only: 0.0.1
      react: 18.3.1
    optionalDependencies:
      '@babel/core': 7.27.4

  stylis@4.2.0: {}

  stylis@4.3.6: {}

  sucrase@3.35.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      commander: 4.1.1
      glob: 10.3.10
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.7
      ts-interface-checker: 0.1.13

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  synckit@0.11.8:
    dependencies:
      '@pkgr/core': 0.2.7

  tabster@8.5.6:
    dependencies:
      keyborg: 2.6.0
      tslib: 2.8.1
    optionalDependencies:
      '@rollup/rollup-linux-x64-gnu': 4.40.0

  tailwindcss@3.4.17:
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.7
      lilconfig: 3.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.1
      postcss: 8.5.6
      postcss-import: 15.1.0(postcss@8.5.6)
      postcss-js: 4.0.1(postcss@8.5.6)
      postcss-load-config: 4.0.2(postcss@8.5.6)
      postcss-nested: 6.2.0(postcss@8.5.6)
      postcss-selector-parser: 6.1.2
      resolve: 1.22.10
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  tapable@2.2.2: {}

  terser-webpack-plugin@5.3.14(webpack@5.99.9):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      jest-worker: 27.5.1
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      terser: 5.42.0
      webpack: 5.99.9

  terser@5.42.0:
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.15.0
      commander: 2.20.3
      source-map-support: 0.5.21

  textarea-caret-ts@4.1.1: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  tlds@1.259.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toggle-selection@1.0.6: {}

  tree-kill@1.2.2: {}

  ts-api-utils@2.1.0(typescript@5.8.3):
    dependencies:
      typescript: 5.8.3

  ts-interface-checker@0.1.13: {}

  ts-loader@9.5.2(typescript@5.8.3)(webpack@5.99.9):
    dependencies:
      chalk: 4.1.2
      enhanced-resolve: 5.18.1
      micromatch: 4.0.8
      semver: 7.7.2
      source-map: 0.7.4
      typescript: 5.8.3
      webpack: 5.99.9

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@2.4.0: {}

  tslib@2.8.1: {}

  tsx@4.20.3:
    dependencies:
      esbuild: 0.25.5
      get-tsconfig: 4.10.1
    optionalDependencies:
      fsevents: 2.3.3

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typescript@5.8.3: {}

  uc.micro@1.0.6: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  update-browserslist-db@1.1.3(browserslist@4.25.0):
    dependencies:
      browserslist: 4.25.0
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  use-callback-ref@1.3.3(@types/react@18.3.23)(react@18.3.1):
    dependencies:
      react: 18.3.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 18.3.23

  use-callback-ref@1.3.3(@types/react@19.1.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.5

  use-debounce@10.0.5(react@18.3.1):
    dependencies:
      react: 18.3.1

  use-sidecar@1.1.3(@types/react@18.3.23)(react@18.3.1):
    dependencies:
      detect-node-es: 1.1.0
      react: 18.3.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 18.3.23

  use-sidecar@1.1.3(@types/react@19.1.5)(react@18.3.1):
    dependencies:
      detect-node-es: 1.1.0
      react: 18.3.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.5

  use-sync-external-store@1.5.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  util-deprecate@1.0.2: {}

  uuid@8.3.2: {}

  uuid@9.0.1: {}

  watchpack@2.4.4:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  webpack-sources@3.3.2: {}

  webpack@5.99.9:
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.15.0
      browserslist: 4.25.0
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.1
      es-module-lexer: 1.7.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.2
      tapable: 2.2.2
      terser-webpack-plugin: 5.3.14(webpack@5.99.9)
      watchpack: 2.4.4
      webpack-sources: 3.3.2
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.19:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  ws@7.5.10: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yaml@1.10.2: {}

  yaml@2.8.0: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}

  zustand@5.0.5(@types/react@19.1.5)(immer@10.1.1)(react@18.3.1)(use-sync-external-store@1.5.0(react@18.3.1)):
    optionalDependencies:
      '@types/react': 19.1.5
      immer: 10.1.1
      react: 18.3.1
      use-sync-external-store: 1.5.0(react@18.3.1)
